import 'package:langchain/langchain.dart';
import 'package:langchain_openai/langchain_openai.dart';
import 'package:novel_app/models/model_config.dart';
import 'package:google_generative_ai/google_generative_ai.dart';

/// 模型适配器，用于根据配置创建不同的LLM实例
class ModelAdapter {
  /// 根据模型配置创建LLM实例
  static ChatLLM createLLMFromConfig(ModelConfig config) {
    if (config.format == 'openai') {
      return OpenAIChatLLM(
        apiKey: config.apiKey,
        baseUrl: config.baseUrl,
        model: config.modelName,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        topP: config.topP,
        frequencyPenalty: config.frequencyPenalty,
        presencePenalty: config.presencePenalty,
        httpClient: _createHttpClient(config),
      );
    } else if (config.format == 'google') {
      return GoogleGenerativeAIChatLLM(
        apiKey: config.apiKey,
        model: config.modelName,
        temperature: config.temperature,
        topP: config.topP,
        httpClient: _createHttpClient(config),
      );
    } else {
      throw Exception('不支持的模型格式: ${config.format}');
    }
  }

  /// 创建HTTP客户端，处理代理设置
  static dynamic _createHttpClient(ModelConfig config) {
    // 这里可以根据需要添加代理设置等
    return null; // 返回null表示使用默认HTTP客户端
  }
}

/// Google Gemini模型的LangChain适配器
class GoogleGenerativeAIChatLLM extends ChatLLM {
  final String apiKey;
  final String model;
  final double temperature;
  final double topP;
  final dynamic httpClient;

  GoogleGenerativeAIChatLLM({
    required this.apiKey,
    required this.model,
    this.temperature = 0.7,
    this.topP = 1.0,
    this.httpClient,
  });

  @override
  Future<ChatResult> invoke(ChatPromptValue input) async {
    try {
      final generativeModel = GenerativeModel(
        model: model,
        apiKey: apiKey,
        generationConfig: GenerationConfig(
          temperature: temperature,
          topP: topP,
        ),
      );

      final messages = input.messages;
      final content = <Content>[];

      for (final message in messages) {
        if (message.role == 'human') {
          content.add(Content.text(message.content.toString()));
        } else if (message.role == 'ai') {
          // 目前Google API不支持AI消息作为输入，所以我们跳过
          continue;
        } else {
          // 对于系统消息，我们将其作为用户消息处理
          content.add(Content.text(message.content.toString()));
        }
      }

      final response = await generativeModel.generateContent(content);
      final responseText = response.text ?? '';

      return ChatResult(
        generations: [
          ChatGeneration(
            output: ChatMessage.ai(responseText),
          ),
        ],
      );
    } catch (e) {
      throw Exception('Google Generative AI调用失败: $e');
    }
  }

  @override
  String get type => 'google-generative-ai';
}
