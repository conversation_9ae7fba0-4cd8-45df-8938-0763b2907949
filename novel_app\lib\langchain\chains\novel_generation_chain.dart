import 'package:langchain/langchain.dart';
import 'package:langchain_openai/langchain_openai.dart';
import 'package:novel_app/langchain/prompts/novel_prompt_templates_enhanced.dart';
import 'package:novel_app/langchain/models/novel_memory.dart';

/// 小说生成链，使用LangChain处理小说生成的核心逻辑
class NovelGenerationChain {
  final BaseChatModel llm;
  final String novelTitle;
  final NovelMemory _novelStorage; // 用于持久化存储
  final ConversationBufferMemory memory; // LangChain 缓冲内存

  // 定义摘要器使用的LLM，可以与主LLM不同，例如使用更快的模型
  // 如果需要，可以从外部传入
  late final BaseChatModel _summarizerLlm;

  NovelGenerationChain({
    required this.llm,
    required this.novelTitle,
    BaseChatModel? summarizerLlm, // 可选的摘要器LLM
  })  : _novelStorage = NovelMemory(novelTitle: novelTitle),
        _summarizerLlm = summarizerLlm ?? llm, // 默认使用主LLM进行摘要
        memory = ConversationBufferMemory(
          memoryKey: 'history', // 对应提示模板中的变量名
          inputKey: 'input', // 标识用户输入（用于 Memory 内部）
          outputKey: 'output', // 标识 AI 输出（用于 Memory 内部）
          humanPrefix: '章节设定', // 更具体的自定义前缀
          aiPrefix: '生成内容', // 更具体的自定义前缀
          returnMessages: false, // 返回字符串历史，而不是消息列表
        );

  /// 获取任务对应的提示模板
  PromptTemplate _getPromptTemplate(String task) {
    switch (task) {
      case 'generate_outline':
        return NovelPromptTemplates.outlineTemplate;
      case 'generate_chapter':
        return NovelPromptTemplates.chapterTemplate;
      case 'continue_novel':
        return NovelPromptTemplates.continueTemplate;
      case 'generate_short_novel':
        return NovelPromptTemplates.shortNovelTemplate;
      case 'generate_short_novel_part':
        // 短篇小说分段生成使用与短篇小说相同的模板
        return NovelPromptTemplates.shortNovelTemplate;
      case 'chat':
        return NovelPromptTemplates.chatTemplate;
      default:
        print("[NovelGenerationChain] 警告: 不支持的任务类型: $task，尝试使用短篇小说模板");
        return NovelPromptTemplates.shortNovelTemplate;
    }
  }

  /// 准备 Chain 执行所需的输入，包括从 Memory 加载历史和格式化提示
  Future<Map<String, dynamic>> _prepareChainInput(String task,
      Map<String, dynamic> userInput, PromptTemplate promptTemplate) async {
    // 确保任务类型不为空
    task = task.isNotEmpty ? task : 'generate_short_novel_part';
    print("\n[NovelGenerationChain] 开始准备Chain输入，任务类型: $task");
    print("[NovelGenerationChain] 收到的用户输入参数: ${userInput.keys.join(', ')}");

    final String memoryInputKey = memory.inputKey!;
    // For outline task, use a generic input, for chapter use chapterTitle
    final String currentInput = (task == 'generate_outline')
        ? 'Generate outline batch starting chapter ${userInput['startChapter']}'
        : userInput['chapterTitle'] ?? userInput['user_request'] ?? '';

    print("[NovelGenerationChain] 当前输入: $currentInput");

    final Map<String, dynamic> memoryLoadInput = {
      memoryInputKey: currentInput,
    };
    final memoryVariables = await memory.loadMemoryVariables(memoryLoadInput);
    print("[NovelGenerationChain] 加载的记忆变量: ${memoryVariables.keys.join(', ')}");

    // Combine user input and memory variables
    final Map<String, dynamic> chainInput = {...userInput, ...memoryVariables};
    print("[NovelGenerationChain] 合并后的输入参数: ${chainInput.keys.join(', ')}");

    // 确保所有必要的参数都存在，特别是章节生成所需的参数
    if (task == 'generate_chapter') {
      print("[NovelGenerationChain] 正在处理章节生成任务，检查必要参数...");

      // 确保必要的参数存在，如果不存在则提供默认值
      chainInput.putIfAbsent('novelTitle', () => novelTitle);
      chainInput.putIfAbsent('genres', () => '未指定类型');
      chainInput.putIfAbsent('theme', () => '未指定主题');
      chainInput.putIfAbsent('targetReaders', () => '通用读者');
      chainInput.putIfAbsent('background', () => '无背景信息');
      chainInput.putIfAbsent('otherRequirements', () => '无其他要求');
      chainInput.putIfAbsent('previousChapterSummary', () => '无上一章摘要');
      chainInput.putIfAbsent('characters', () => '无角色信息');
      chainInput.putIfAbsent('writingStylePrompt', () => '无特定文风要求');
      chainInput.putIfAbsent('knowledgeBase', () => '无知识库信息');

      // 检查章节号是否为字符串，如果是则转换为整数
      if (chainInput.containsKey('chapterNumber') &&
          chainInput['chapterNumber'] is String) {
        try {
          final chapterNumberStr = chainInput['chapterNumber'] as String;
          final chapterNumber = int.tryParse(chapterNumberStr);
          if (chapterNumber != null) {
            print(
                "[NovelGenerationChain] 将章节号从字符串'$chapterNumberStr'转换为整数$chapterNumber");
            // 保留字符串版本，因为模板可能期望字符串
          } else {
            print("[NovelGenerationChain] 警告：无法将章节号'$chapterNumberStr'解析为整数");
          }
        } catch (e) {
          print("[NovelGenerationChain] 章节号转换错误: $e");
        }
      }

      try {
        final outlineJson = await _novelStorage.getOutline();
        if (outlineJson != null && outlineJson.isNotEmpty) {
          print("[NovelGenerationChain] 已检索到存储的大纲 (长度: ${outlineJson.length})");
        }
      } catch (e) {
        print("[NovelGenerationChain] 检索章节上下文大纲时出错: $e");
      }
    } else if (task == 'continue_novel') {
      // 确保续写所需的参数存在
      chainInput.putIfAbsent('existingContent', () => '无现有内容');
      chainInput.putIfAbsent('writingStylePrompt', () => '无特定文风要求');
      chainInput.putIfAbsent('knowledgeBase', () => '无知识库信息');
    } else if (task == 'generate_short_novel' ||
        task == 'generate_short_novel_part') {
      // 确保短篇小说生成所需的参数存在
      print("[NovelGenerationChain] 处理短篇小说生成任务，检查必要参数...");

      // 添加所有必需的模板变量，确保它们不为null
      chainInput.putIfAbsent(
          'novelTitle', () => chainInput['novelTitle'] ?? '无标题短篇小说');
      chainInput.putIfAbsent(
          'genres', () => chainInput['genres'] ?? ['奇幻', '冒险']);
      chainInput.putIfAbsent('theme', () => chainInput['theme'] ?? '成长与冒险');
      chainInput.putIfAbsent(
          'targetReaders', () => chainInput['targetReaders'] ?? '所有年龄段读者');
      chainInput.putIfAbsent('wordCount', () => '3000');
      chainInput.putIfAbsent('background', () => '无背景信息');
      chainInput.putIfAbsent('otherRequirements', () => '无其他要求');
      chainInput.putIfAbsent('characters', () => '无角色信息');
      chainInput.putIfAbsent('writingStylePrompt', () => '无特定文风要求');
      chainInput.putIfAbsent('knowledgeBase', () => '无知识库信息');

      // 短篇小说分段生成的特殊参数
      if (task == 'generate_short_novel_part') {
        chainInput.putIfAbsent('partNumber', () => '1');
        chainInput.putIfAbsent('totalParts', () => '1');
        chainInput.putIfAbsent('startPercentage', () => '0');
        chainInput.putIfAbsent('endPercentage', () => '100');
        chainInput.putIfAbsent('currentPartSummary', () => '无当前部分概要');
        chainInput.putIfAbsent('outline', () => '无大纲信息');

        print("[NovelGenerationChain] 短篇小说分段生成参数:");
        print(
            "[NovelGenerationChain] - partNumber: ${chainInput['partNumber']}");
        print(
            "[NovelGenerationChain] - totalParts: ${chainInput['totalParts']}");
        print(
            "[NovelGenerationChain] - startPercentage: ${chainInput['startPercentage']}");
        print(
            "[NovelGenerationChain] - endPercentage: ${chainInput['endPercentage']}");
        print(
            "[NovelGenerationChain] - currentPartSummary: ${chainInput['currentPartSummary']?.toString().length ?? 0}字");
        print(
            "[NovelGenerationChain] - outline: ${chainInput['outline']?.toString().length ?? 0}字");
      }

      // 如果genres是数组，转换为字符串
      if (chainInput['genres'] is List) {
        chainInput['genres'] = (chainInput['genres'] as List).join(', ');
      }

      // 打印所有输入参数
      print("[NovelGenerationChain] 短篇小说生成参数:");
      chainInput.forEach((key, value) {
        final valueStr = value.toString();
        final displayValue = valueStr.length > 50
            ? '${valueStr.substring(0, 50)}...'
            : valueStr; // Truncate
        print("[NovelGenerationChain] - $key: $displayValue");
      });
    }

    // 确保 history 变量存在，如果不存在则使用空字符串
    if (!chainInput.containsKey('history') ||
        chainInput['history'] == null ||
        chainInput['history'].toString().isEmpty) {
      print("[NovelGenerationChain] 警告：没有提供 history 变量，使用空字符串代替。");
      chainInput['history'] = '';
    } else {
      print(
          "[NovelGenerationChain] 已提供 history 变量，长度: ${chainInput['history'].toString().length}");
    }

    // Add batch keys if missing for outline task (should be provided by service)
    if (task == 'generate_outline') {
      chainInput.putIfAbsent('startChapter', () => 1);
      chainInput.putIfAbsent(
          'endChapter', () => userInput['totalChapters'] ?? 1);
    }

    // Format the prompt using the combined input
    // Use a try-catch for robust formatting, especially with complex inputs
    String formattedPrompt;
    try {
      // --- Add detailed logging before formatting ---
      print('[NovelGenerationChain._prepareChainInput] --- 格式化输入 --- 开始 ---');
      chainInput.forEach((key, value) {
        final valueStr = value.toString();
        final displayValue = valueStr.length > 200
            ? '${valueStr.substring(0, 200)}...'
            : valueStr; // Truncate
        print('  [$key]: $displayValue');
      });
      print('[NovelGenerationChain._prepareChainInput] --- 格式化输入 --- 结束 ---');

      print("[NovelGenerationChain] 使用模板格式化提示词...");
      formattedPrompt = promptTemplate.format(chainInput);
      print("[NovelGenerationChain] 提示词格式化成功，长度: ${formattedPrompt.length}");
    } catch (e) {
      print("[NovelGenerationChain] 格式化提示词时出错: $e");
      print("[NovelGenerationChain] 链输入键: ${chainInput.keys.join(', ')}");
      print(
          "[NovelGenerationChain] 模板变量: ${promptTemplate.inputVariables.join(', ')}");

      // 检查是否缺少必要的变量
      for (final variable in promptTemplate.inputVariables) {
        if (!chainInput.containsKey(variable)) {
          print("[NovelGenerationChain] 错误：缺少必要的模板变量: '$variable'");
        }
      }

      rethrow; // 重新抛出异常
    }

    final Map<String, dynamic> resultInput = {
      'formatted_prompt': formattedPrompt,
      memory.inputKey!:
          currentInput, // Ensure memory input key is set correctly
      ...chainInput // Include all prepared inputs for potential use by the chain/LLM call itself
    };

    // Clean up any null values just in case
    resultInput.removeWhere((key, value) => value == null);

    return resultInput;
  }

  /// 同步执行链并返回结果
  Future<String> run(Map<String, dynamic> userInput) async {
    print("\n[NovelGenerationChain] 开始执行链，同步模式");
    final task = userInput['task'] as String? ?? 'generate_short_novel_part';
    print("[NovelGenerationChain] 任务类型: $task");

    final promptTemplate = _getPromptTemplate(task);
    print(
        "[NovelGenerationChain] 已获取提示模板，模板变量: ${promptTemplate.inputVariables.join(', ')}");

    final chainInput =
        await _prepareChainInput(task, userInput, promptTemplate);
    print("[NovelGenerationChain] 已准备链输入，包含${chainInput.length}个参数");

    final messages = [
      ChatMessage.humanText(chainInput['formatted_prompt'] ?? '')
    ];
    final promptValue = ChatPromptValue(messages);
    print("[NovelGenerationChain] 已创建提示值，准备调用LLM...");

    // 检查LLM类型
    if (llm.runtimeType.toString().contains('AliyunQwenAdapter')) {
      print("[NovelGenerationChain] 检测到阿里云通义千问适配器");
    } else if (llm.toString().contains('dashscope.aliyuncs.com')) {
      print("[NovelGenerationChain] 检测到阿里云通义千问模型");
    }

    String result = '';
    String outputForMemory = '';

    // 无论什么模型，都使用流式模式，这样可以确保阿里云通义千问模型正常工作
    print("[NovelGenerationChain] 使用流式模式调用LLM生成内容...");
    final buffer = StringBuffer();
    final streamingResponse = llm.stream(promptValue);

    await for (final chunk in streamingResponse) {
      try {
        if (chunk.generations.isNotEmpty) {
          final output = chunk.generations.first.output;
          String contentChunk = '';

          // 获取内容
          try {
            // 尝试获取 contentAsString 属性
            final dynamic dynamicOutput = output;

            // 检查是否有 contentAsString 属性
            if (dynamicOutput != null) {
              try {
                // 尝试直接访问 contentAsString 属性
                contentChunk = dynamicOutput.contentAsString;
              } catch (_) {
                // 如果没有 contentAsString 属性，使用 toString()
                contentChunk = dynamicOutput.toString();

                // 如果内容包含 ChatMessage 类名，尝试清理
                if (contentChunk.contains('ChatMessage')) {
                  // 尝试提取 content 字段
                  final contentMatch =
                      RegExp(r'content:\s*(.*?),').firstMatch(contentChunk);
                  if (contentMatch != null && contentMatch.groupCount >= 1) {
                    contentChunk = contentMatch.group(1) ?? contentChunk;
                  }
                }
              }
            }
          } catch (e) {
            print("[NovelGenerationChain] 警告: 无法获取内容块: $e");
            contentChunk = ""; // 使用空字符串作为默认值
          }

          if (contentChunk.isNotEmpty) {
            buffer.write(contentChunk);
          }
        }
      } catch (e) {
        print("[NovelGenerationChain] 处理内容块时出错: $e");
        // 继续处理下一个块
      }
    }

    result = buffer.toString();
    outputForMemory = result;
    print("[NovelGenerationChain] 流式生成完成，内容长度: ${result.length}");

    final Map<String, dynamic> contextToSaveInput = {
      memory.inputKey!: chainInput[memory.inputKey!] ?? ''
    };
    final Map<String, dynamic> contextToSaveOutput = {
      memory.outputKey!: outputForMemory
    };

    await _saveToMemory(
        task, userInput, contextToSaveInput, contextToSaveOutput, result);

    return result;
  }

  /// 流式执行链并返回结果流
  Stream<String> stream(Map<String, dynamic> userInput) async* {
    print("\n[NovelGenerationChain] 开始执行链，流式模式");
    final task = userInput['task'] as String? ?? 'generate_short_novel_part';
    print("[NovelGenerationChain] 任务类型: $task");

    final promptTemplate = _getPromptTemplate(task);
    print(
        "[NovelGenerationChain] 已获取提示模板，模板变量: ${promptTemplate.inputVariables.join(', ')}");

    final chainInput =
        await _prepareChainInput(task, userInput, promptTemplate);
    print("[NovelGenerationChain] 已准备链输入，包含${chainInput.length}个参数");

    final messages = [
      ChatMessage.humanText(chainInput['formatted_prompt'] ?? '')
    ];
    final promptValue = ChatPromptValue(messages);
    print("[NovelGenerationChain] 已创建提示值，准备流式调用LLM...");

    String fullResponse = '';
    print("[NovelGenerationChain] 开始流式生成内容...");
    final streamingResponse = llm.stream(promptValue);

    int chunkCount = 0;
    await for (final chunk in streamingResponse) {
      try {
        if (chunk.generations.isNotEmpty) {
          final output = chunk.generations.first.output;
          String contentChunk = '';

          // 获取内容
          try {
            // 尝试获取 contentAsString 属性
            final dynamic dynamicOutput = output;

            // 检查是否有 contentAsString 属性
            if (dynamicOutput != null) {
              try {
                // 尝试直接访问 contentAsString 属性
                contentChunk = dynamicOutput.contentAsString;
              } catch (_) {
                // 如果没有 contentAsString 属性，使用 toString()
                contentChunk = dynamicOutput.toString();

                // 如果内容包含 ChatMessage 类名，尝试清理
                if (contentChunk.contains('ChatMessage')) {
                  // 尝试提取 content 字段
                  final contentMatch =
                      RegExp(r'content:\s*(.*?),').firstMatch(contentChunk);
                  if (contentMatch != null && contentMatch.groupCount >= 1) {
                    contentChunk = contentMatch.group(1) ?? contentChunk;
                  }
                }
              }
            }
          } catch (e) {
            print("[NovelGenerationChain] 警告: 无法获取内容块: $e");
            contentChunk = ""; // 使用空字符串作为默认值
          }

          if (contentChunk.isNotEmpty) {
            fullResponse += contentChunk;
            chunkCount++;
            if (chunkCount % 10 == 0) {
              print(
                  "[NovelGenerationChain] 已接收$chunkCount个内容块，当前总长度: ${fullResponse.length}");
            }
            yield contentChunk;
          }
        }
      } catch (e) {
        print("[NovelGenerationChain] 处理内容块时出错: $e");
        // 继续处理下一个块
      }
    }
    print(
        "[NovelGenerationChain] 流式生成完成，共接收$chunkCount个内容块，总长度: ${fullResponse.length}");

    final Map<String, dynamic> contextToSaveInput = {
      memory.inputKey!: chainInput[memory.inputKey!] ?? ''
    };
    final Map<String, dynamic> contextToSaveOutput = {
      memory.outputKey!: fullResponse
    };

    await _saveToMemory(
        task, userInput, contextToSaveInput, contextToSaveOutput, fullResponse);
  }

  /// 保存结果到 LangChain Memory 和 NovelMemory
  Future<void> _saveToMemory(
      String task,
      Map<String, dynamic> originalUserInput,
      Map<String, dynamic> contextToSaveInput,
      Map<String, dynamic> contextToSaveOutput,
      String result) async {
    print("\n[NovelGenerationChain] 开始保存结果到内存，任务类型: $task");

    // 1. 保存到 LangChain Memory (对话历史) - Always do this
    try {
      // 在保存前打印详细信息
      print("[NovelGenerationChain] 准备保存上下文到LangChain内存");
      print(
          "[NovelGenerationChain] 输入键: ${contextToSaveInput.keys.join(', ')}");
      print(
          "[NovelGenerationChain] 输出键: ${contextToSaveOutput.keys.join(', ')}");

      // 确保输入和输出不为空
      if (contextToSaveInput.isEmpty || contextToSaveOutput.isEmpty) {
        print("[NovelGenerationChain] 警告: 保存到LangChain内存的上下文为空");
      }

      // 打印输入和输出的长度
      contextToSaveInput.forEach((key, value) {
        final valueStr = value.toString();
        print("[NovelGenerationChain] 输入[$key]长度: ${valueStr.length}");
      });

      contextToSaveOutput.forEach((key, value) {
        final valueStr = value.toString();
        print("[NovelGenerationChain] 输出[$key]长度: ${valueStr.length}");
      });

      await memory.saveContext(
          inputValues: contextToSaveInput, outputValues: contextToSaveOutput);
      print("[NovelGenerationChain] 上下文已成功保存到LangChain内存。");

      // 加载内存变量以验证保存是否成功
      final memoryLoadInput = {memory.inputKey!: 'test'};
      final memoryVariables = await memory.loadMemoryVariables(memoryLoadInput);
      if (memoryVariables.containsKey('history')) {
        final historyStr = memoryVariables['history'].toString();
        print("[NovelGenerationChain] 验证: 已保存的历史记录长度: ${historyStr.length}");
      } else {
        print("[NovelGenerationChain] 警告: 无法验证历史记录是否保存成功");
      }
    } catch (e) {
      print("[NovelGenerationChain] 保存上下文到LangChain内存时出错: $e");
    }

    // 2. 根据任务类型保存到 NovelMemory (持久化存储)
    // IMPORTANT: Outline is now saved by the service layer after aggregation.
    if (task == 'generate_chapter') {
      try {
        print("[NovelGenerationChain] 准备保存章节内容到NovelMemory...");
        // FIX: Parse chapter number from string before saving
        final chapterNumberString =
            originalUserInput['chapterNumber'] as String? ?? '0';
        print("[NovelGenerationChain] 原始章节号: '$chapterNumberString'");

        final chapterNumber =
            int.tryParse(chapterNumberString) ?? 0; // Use tryParse for safety
        if (chapterNumber == 0 && chapterNumberString != '0') {
          // Handle potential parse failure
          print(
              "[NovelGenerationChain] 警告：无法将章节号'$chapterNumberString'解析为整数用于保存。");
          // Decide how to handle: skip saving? throw error? For now, log and continue with 0
        }

        final chapterTitle =
            originalUserInput['chapterTitle'] as String? ?? "未命名章节";
        print(
            "[NovelGenerationChain] 保存章节 $chapterNumber ('$chapterTitle')，内容长度: ${result.length}");

        await _novelStorage.saveChapter(chapterNumber, chapterTitle, result);
        print(
            "[NovelGenerationChain] 章节 $chapterNumber ('$chapterTitle') 已保存到NovelMemory。");
      } catch (e) {
        // Enhanced error logging
        print(
            "[NovelGenerationChain] 保存章节到NovelMemory时出错 (编号: ${originalUserInput['chapterNumber']}, 标题: ${originalUserInput['chapterTitle']}): $e");
      }
    } else if (task == 'continue_novel') {
      try {
        print(
            "[NovelGenerationChain] 保存续写内容到NovelMemory，内容长度: ${result.length}");
        await _novelStorage.saveContinuation(result);
        print("[NovelGenerationChain] 续写内容已保存到NovelMemory。");
      } catch (e) {
        print("[NovelGenerationChain] 保存续写内容到NovelMemory时出错: $e");
      }
    } else if (task == 'generate_short_novel') {
      try {
        print(
            "[NovelGenerationChain] 保存短篇小说到NovelMemory，内容长度: ${result.length}");
        await _novelStorage.saveShortNovel(result);
        print("[NovelGenerationChain] 短篇小说已保存到NovelMemory。");
      } catch (e) {
        print("[NovelGenerationChain] 保存短篇小说到NovelMemory时出错: $e");
      }
    } else if (task == 'generate_short_novel_part') {
      try {
        // 获取部分编号
        final partNumber =
            int.tryParse(originalUserInput['partNumber'] as String? ?? '1') ??
                1;
        final partTitle = "第$partNumber部分";

        print(
            "[NovelGenerationChain] 保存短篇小说部分到NovelMemory，部分: $partNumber，内容长度: ${result.length}");

        // 使用章节保存方法来保存短篇小说部分
        await _novelStorage.saveChapter(partNumber, partTitle, result);

        print("[NovelGenerationChain] 短篇小说部分已保存到NovelMemory。");
      } catch (e) {
        print("[NovelGenerationChain] 保存短篇小说部分到NovelMemory时出错: $e");
      }
    } else if (task == 'generate_outline') {
      // Do nothing here for outline generation - it's handled by the service now.
      print("[NovelGenerationChain] 大纲生成任务已完成批次。不保存部分结果到NovelMemory。");
    }

    print("[NovelGenerationChain] 内存保存完成。");
  }
}
