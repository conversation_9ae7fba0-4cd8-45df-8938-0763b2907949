import 'dart:async';
import 'dart:convert';
import 'dart:math'; // Import math for min function
import 'package:get/get.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/controllers/knowledge_base_controller.dart';
import 'package:novel_app/models/writing_style_package.dart';
import 'package:novel_app/models/character_card.dart';
import 'package:novel_app/langchain/chains/novel_generation_chain.dart';
import 'package:novel_app/langchain/utils/model_adapter.dart';
import 'package:novel_app/langchain/chains/detailed_outline_chain.dart';
import 'package:novel_app/langchain/models/novel_memory.dart'; // Import NovelMemory
import 'package:novel_app/langchain/prompts/novel_prompt_templates_enhanced.dart';
import 'package:novel_app/services/chat_history_service.dart';
import 'package:novel_app/models/chat_message.dart' as app_chat;
import 'package:novel_app/services/embedding_service.dart';
import 'package:langchain/langchain.dart';
import 'package:langchain_openai/langchain_openai.dart';

/// 使用LangChain实现的小说生成服务
class NovelGenerationService extends GetxService {
  final ApiConfigController _apiConfigController;
  late final EmbeddingService _embeddingService;

  // 缓存生成链，避免重复创建
  final Map<String, NovelGenerationChain> _chainCache = {};
  final Map<String, DetailedOutlineChain> _detailedChainCache = {};

  // Define batch size for outline generation
  static const int _outlineBatchSize = 20; // Generate 20 chapters per batch

  NovelGenerationService({
    required ApiConfigController apiConfigController,
  }) : _apiConfigController = apiConfigController {
    _embeddingService =
        Get.put(EmbeddingService(apiConfigController: apiConfigController));
  }

  /// 创建或获取已有的生成链
  Future<NovelGenerationChain> _getChain(String novelTitle) async {
    if (_chainCache.containsKey(novelTitle)) {
      return _chainCache[novelTitle]!;
    }
    final modelConfig = _apiConfigController.getCurrentModel();
    final llm = ModelAdapter.createLLMFromConfig(modelConfig);
    final chain = NovelGenerationChain(
      llm: llm,
      novelTitle: novelTitle,
      // Optionally pass a different summarizer LLM if needed
    );
    _chainCache[novelTitle] = chain;
    return chain;
  }

  /// 创建或获取已有的详细大纲生成链
  Future<DetailedOutlineChain> _getDetailedChain(String novelTitle) async {
    if (_detailedChainCache.containsKey(novelTitle)) {
      return _detailedChainCache[novelTitle]!;
    }
    final modelConfig = _apiConfigController.getCurrentModel();
    final llm = ModelAdapter.createLLMFromConfig(modelConfig);
    final chain = DetailedOutlineChain(llm: llm);
    _detailedChainCache[novelTitle] = chain;
    return chain;
  }

  /// Helper to get NovelMemory associated with a novel title
  NovelMemory _getNovelMemory(String novelTitle, {String? sessionId}) {
    // This is a simplified approach. Ideally, NovelGenerationChain would expose its memory.
    return NovelMemory(novelTitle: novelTitle, sessionId: sessionId);
  }

  /// 清除缓存的生成链
  void clearChain(String novelTitle) {
    _chainCache.remove(novelTitle);
    _detailedChainCache.remove(novelTitle);
    // Consider clearing NovelMemory here too if it's tightly coupled
    // await _getNovelMemory(novelTitle).clear(); // Example
  }

  /// 清除所有缓存的生成链
  void clearAllChains() {
    _chainCache.clear();
    _detailedChainCache.clear();
    print('已清除所有缓存的 NovelGenerationChain 和 DetailedOutlineChain 实例');
    // Consider clearing all NovelMemory instances
  }

  /// 获取聊天链，用于生成对话回复
  Future<NovelGenerationChain> getChatChain(String novelTitle) async {
    return _getChain(novelTitle);
  }

  /// Helper to build character details string (Corrected field names)
  String _buildCharacterDetailsString(dynamic cards) {
    // 处理空输入
    if (cards == null) return '无角色设定';

    // 处理 Map<String, CharacterCard> 类型
    if (cards is Map<String, CharacterCard>) {
      if (cards.isEmpty) return '无角色设定';
      return cards.entries.map((entry) {
        final card = entry.value;
        // Use correct field names based on linter errors and common patterns
        return '''
- ${card.name}:
  性别: ${card.gender ?? '未指定'}
  年龄: ${card.age ?? '未指定'}
  性格: ${card.personalityTraits ?? '未指定'}
  外貌: ${card.appearance ?? '未指定'}
  背景: ${card.background ?? '未指定'}
  技能: ${card.abilities ?? '未指定'}
  目标: ${card.motivation ?? '未指定'}
''';
      }).join('\n');
    }

    // 处理 List<CharacterCard> 类型
    if (cards is List<CharacterCard>) {
      if (cards.isEmpty) return '无角色设定';
      return cards.map((card) {
        return '''
- ${card.name}:
  性别: ${card.gender ?? '未指定'}
  年龄: ${card.age ?? '未指定'}
  性格: ${card.personalityTraits ?? '未指定'}
  外貌: ${card.appearance ?? '未指定'}
  背景: ${card.background ?? '未指定'}
  技能: ${card.abilities ?? '未指定'}
  目标: ${card.motivation ?? '未指定'}
''';
      }).join('\n');
    }

    // 处理其他类型
    return '无角色设定';
  }

  /// 生成小说大纲 (Batched JSON Version)
  /// Returns the complete outline as a JSON string.
  Future<String> generateOutline({
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required int totalChapters,
    String? background,
    String? otherRequirements,
    WritingStylePackage? writingStyle,
    Map<String, CharacterCard>? characterCards,
    void Function(int currentBatch, int totalBatches)? onProgress,
  }) async {
    if (totalChapters <= 0) {
      throw ArgumentError('总章节数必须大于 0。');
    }

    print(
        "[NovelGenerationService.generateOutline] 开始为 '$novelTitle' ($totalChapters 章) 生成分批大纲。批次大小: $_outlineBatchSize");

    // 获取当前模型配置，确保使用正确的API路径
    final modelConfig = _apiConfigController.getCurrentModel();
    print('[NovelGenerationService.generateOutline] 使用模型: ${modelConfig.name}');
    print(
        '[NovelGenerationService.generateOutline] API格式: ${modelConfig.apiFormat}');
    print(
        '[NovelGenerationService.generateOutline] API路径: ${modelConfig.apiPath}');
    print(
        '[NovelGenerationService.generateOutline] 使用代理: ${modelConfig.useProxy}');

    // 如果是Google API，确保路径正确
    if (modelConfig.apiFormat == 'Google API' &&
        !modelConfig.apiPath.contains(':generateContent')) {
      // 自动修正路径
      final correctPath = '/v1beta/models/${modelConfig.model}:generateContent';
      print(
          '[NovelGenerationService.generateOutline] 自动修正Google API路径为: $correctPath');
      await _apiConfigController.updateModelConfig(
        modelConfig.name,
        apiPath: correctPath,
      );
    }

    final chain = await _getChain(novelTitle);
    final novelMemory =
        _getNovelMemory(novelTitle, sessionId: null); // Get memory instance

    // Prepare base input common to all batches
    String charactersString =
        _buildCharacterDetailsString(characterCards ?? {});
    String writingStylePromptString = writingStyle?.getPrompt() ?? '';
    String knowledgeBaseString = '';
    final knowledgeBaseController = Get.find<KnowledgeBaseController>();
    if (knowledgeBaseController.useKnowledgeBase.value &&
        knowledgeBaseController.selectedDocIds.isNotEmpty) {
      knowledgeBaseString = knowledgeBaseController.getSelectedDocsContent();
    }

    final baseInput = {
      'novelTitle': novelTitle,
      'genres': genres.join(', '),
      'theme': theme ?? '无主题',
      'targetReaders': targetReaders ?? '通用',
      'totalChapters': totalChapters.toString(), // Keep total for context
      'background': background ?? '无背景',
      'otherRequirements': otherRequirements ?? '无其他要求',
      'characters': charactersString,
      'writingStylePrompt': writingStylePromptString.isNotEmpty
          ? writingStylePromptString
          : '无特定文风要求',
      'knowledgeBase':
          knowledgeBaseString.isNotEmpty ? knowledgeBaseString : '无知识库信息',
      'task': 'generate_outline',
    };

    List<Map<String, dynamic>> allChapters = [];
    int numberOfBatches = (totalChapters / _outlineBatchSize).ceil();

    for (int i = 0; i < numberOfBatches; i++) {
      int startChapter = i * _outlineBatchSize + 1;
      int endChapter = min((i + 1) * _outlineBatchSize, totalChapters);
      print(
          "[NovelGenerationService.generateOutline] 处理批次 ${i + 1}/$numberOfBatches (章节 $startChapter-$endChapter)...");
      onProgress?.call(i + 1, numberOfBatches);

      final batchInput = {
        ...baseInput,
        'startChapter': startChapter.toString(),
        'endChapter': endChapter.toString(),
      };

      int retryCount = 0;
      const int maxRetries = 2; // Allow up to 2 retries per batch
      bool batchSuccess = false;

      while (retryCount <= maxRetries && !batchSuccess) {
        try {
          // 使用流式模式而不是run方法，以支持阿里云通义千问模型
          print(
              "[NovelGenerationService.generateOutline] 使用流式模式生成批次 ${i + 1} (尝试 ${retryCount + 1})...");
          final buffer = StringBuffer();
          final resultStream = chain.stream(batchInput);

          await for (final chunk in resultStream) {
            buffer.write(chunk);
          }

          final rawResult = buffer.toString();
          print(
              "[NovelGenerationService.generateOutline] 批次 ${i + 1} (尝试 ${retryCount + 1}) 原始结果长度: ${rawResult.length}");

          dynamic decodedResult;
          try {
            String cleanedResult = rawResult.trim();
            // More robust cleaning for potential ```json ... ``` markers
            if (cleanedResult.startsWith("```json")) {
              cleanedResult = cleanedResult.substring(7).trim();
            }
            if (cleanedResult.endsWith("```")) {
              cleanedResult =
                  cleanedResult.substring(0, cleanedResult.length - 3).trim();
            }

            if (!cleanedResult.startsWith('[') ||
                !cleanedResult.endsWith(']')) {
              throw FormatException(
                  "LLM 未返回有效的 JSON 数组 (未找到 '[' 或 ']'). Raw:\n$cleanedResult");
            }

            decodedResult = jsonDecode(cleanedResult);
            // --- Add Diagnostic Logging ---
            print(
                "[DEBUG] Batch ${i + 1} decodedResult Type: ${decodedResult.runtimeType}");
            // print("[DEBUG] Batch ${i + 1} decodedResult Content: $decodedResult"); // Be cautious logging large content
            // --- End Diagnostic Logging ---
          } on FormatException catch (e) {
            print(
                "[NovelGenerationService.generateOutline] 错误: 批次 ${i + 1} (尝试 ${retryCount + 1}) JSON 解码失败. 错误: $e.");
            // Don't rethrow immediately, allow retry
            rethrow;
          }

          if (decodedResult is List) {
            int chaptersInBatch = 0;
            List<Map<String, dynamic>> batchChapters = [];
            for (var item in decodedResult) {
              if (item is Map<String, dynamic> &&
                  item.containsKey('chapterNumber') &&
                  item['chapterNumber'] is int &&
                  item.containsKey('chapterTitle') &&
                  item['chapterTitle'] is String &&
                  item.containsKey('summary') &&
                  item['summary'] is String &&
                  // Additional check: ensure chapter number is within expected range for the batch
                  item['chapterNumber'] >= startChapter &&
                  item['chapterNumber'] <= endChapter) {
                batchChapters.add(item);
                chaptersInBatch++;
              } else {
                print(
                    "[NovelGenerationService.generateOutline] 警告: 批次 ${i + 1} 中发现无效或超出范围的章节结构: $item. 跳过.");
              }
            }
            // Validate if the number of chapters is reasonable for the batch
            if (chaptersInBatch != (endChapter - startChapter + 1)) {
              print(
                  "[NovelGenerationService.generateOutline] 警告: 批次 ${i + 1} 解析出的章节数 ($chaptersInBatch) 与预期 (${endChapter - startChapter + 1}) 不符.");
              // Decide if this constitutes a failure requiring retry
              // For now, accept the parsed chapters but log warning
            }
            allChapters.addAll(batchChapters);
            // --- Add Diagnostic Logging ---
            print(
                "[DEBUG] Batch ${i + 1} added ${batchChapters.length} chapters. Current allChapters length: ${allChapters.length}");
            // Check if nesting occurred IMMEDIATELY after addAll
            if (allChapters.isNotEmpty && allChapters.first is List) {
              print(
                  "[DEBUG] *** WARNING: Nesting detected in allChapters immediately after batch ${i + 1} addAll! ***");
            }
            // --- End Diagnostic Logging ---
            print(
                "[NovelGenerationService.generateOutline] 批次 ${i + 1} (尝试 ${retryCount + 1}) 成功解析. 添加了 $chaptersInBatch 个章节.");
            batchSuccess = true; // Mark batch as successful
          } else {
            throw FormatException(
                "解码结果不是 List 类型. 类型: ${decodedResult.runtimeType}");
          }
        } catch (e) {
          print(
              "[NovelGenerationService.generateOutline] 错误: 处理批次 ${i + 1} (尝试 ${retryCount + 1}) 失败: $e");
          retryCount++;
          if (retryCount > maxRetries) {
            print(
                "[NovelGenerationService.generateOutline] 错误: 批次 ${i + 1} 达到最大重试次数 ($maxRetries). 大纲生成失败.");
            throw Exception("处理大纲批次 ${i + 1} 失败，已达到最大重试次数.");
          } else {
            print(
                "[NovelGenerationService.generateOutline] 在 ${3 * retryCount} 秒后重试批次 ${i + 1}...");
            await Future.delayed(Duration(
                seconds: 3 * retryCount)); // Exponential backoff (simple)
          }
        }
      } // End retry loop
    } // End batch loop

    // --- Aggregation and Saving ---
    if (allChapters.length != totalChapters) {
      print(
          "[NovelGenerationService.generateOutline] 警告: 最终章节计数 (${allChapters.length}) 与请求的总数 ($totalChapters) 不符. 可能缺少或解析失败了部分章节.");
    }

    allChapters.sort((a, b) =>
        (a['chapterNumber'] as int).compareTo(b['chapterNumber'] as int));

    final finalOutlineJson = {
      'novelTitle': novelTitle,
      'chapters': allChapters,
      'generatedAt': DateTime.now().toIso8601String(),
      'totalChaptersGenerated':
          allChapters.length, // Store the actual count generated
      'totalChaptersRequested': totalChapters,
    };

    String finalJsonString;
    try {
      finalJsonString = jsonEncode(finalOutlineJson);
      print(
          "[NovelGenerationService.generateOutline] 最终聚合的大纲 JSON 已生成 (长度: ${finalJsonString.length}).");
    } catch (e) {
      print("[NovelGenerationService.generateOutline] 错误: 无法编码最终 JSON 大纲: $e");
      throw Exception("无法创建最终的大纲 JSON 结构.");
    }

    // Save the complete JSON outline OBJECT string to NovelMemory
    try {
      await novelMemory.saveOutline(finalJsonString);
      print(
          "[NovelGenerationService.generateOutline] 完整 JSON 大纲已保存到 NovelMemory ('$novelTitle').");
    } catch (e) {
      print(
          "[NovelGenerationService.generateOutline] 错误: 无法将最终 JSON 大纲保存到 NovelMemory: $e");
      throw Exception("无法保存生成的大纲.");
    }

    // 保存大纲生成记录到对话历史
    final userMessage = app_chat.ChatMessage.user(
      content: "生成《$novelTitle》的大纲，共$totalChapters章",
      novelTitle: novelTitle,
    );
    await Get.find<ChatHistoryService>().addMessage(userMessage);

    // 生成简单的大纲摘要作为AI回复
    final chapters = finalOutlineJson['chapters'] as List<dynamic>? ?? [];
    final summaryBuffer =
        StringBuffer("已生成《$novelTitle》的大纲，共${chapters.length}章:\n");
    for (var chapter in chapters) {
      if (chapter is Map<String, dynamic>) {
        final chapterNumber = chapter['chapterNumber'];
        final chapterTitle = chapter['chapterTitle'];
        summaryBuffer.writeln("- 第$chapterNumber章：$chapterTitle");
      }
    }

    final aiMessage = app_chat.ChatMessage.ai(
      content: summaryBuffer.toString(),
      novelTitle: novelTitle,
    );
    await Get.find<ChatHistoryService>().addMessage(aiMessage);

    // Return ONLY the JSON array string of chapters for the UI/Controller
    try {
      final List<dynamic> chaptersList =
          finalOutlineJson['chapters'] as List<dynamic>? ?? [];
      final String chaptersJsonArrayString = jsonEncode(chaptersList);
      print(
          "[NovelGenerationService.generateOutline] 返回纯章节 JSON 数组字符串 (长度: ${chaptersJsonArrayString.length}).");
      return chaptersJsonArrayString;
    } catch (e) {
      print("[NovelGenerationService.generateOutline] 错误: 无法编码纯章节 JSON 数组: $e");
      // Fallback to returning the full string if encoding chapters fails?
      // Or rethrow? For now, rethrow.
      throw Exception("无法为 UI 准备章节 JSON 数组.");
    }

    // return finalJsonString; // Old: Return the full JSON string
  }

  /// Generates a detailed Markdown outline for a specific chapter using DetailedOutlineChain.
  Future<String> generateDetailedChapterOutline({
    required String novelTitle,
    required int chapterNumber,
    // Required context for the detailed outline prompt
    required List<String> genres,
    required String theme,
    required String targetReaders,
    String? background,
    String? otherRequirements,
    WritingStylePackage? writingStyle,
    Map<String, CharacterCard>? characterCards,
  }) async {
    print(
        "[NovelGenerationService.generateDetailedChapterOutline] 为 '$novelTitle', 第 $chapterNumber 章 生成详细大纲...");

    final detailedChain = await _getDetailedChain(novelTitle);
    final novelMemory = _getNovelMemory(novelTitle, sessionId: null);

    // 1. Retrieve and parse the stored full JSON outline
    Map<String, dynamic> fullOutline;
    try {
      final outlineJsonString = await novelMemory.getOutline();
      if (outlineJsonString == null || outlineJsonString.isEmpty) {
        throw Exception("未找到或为空: '$novelTitle' 的已存储大纲.");
      }
      fullOutline = jsonDecode(outlineJsonString) as Map<String, dynamic>;
    } catch (e) {
      print(
          "[NovelGenerationService.generateDetailedChapterOutline] 错误: 检索/解析已存储大纲失败: $e");
      throw Exception("无法加载或解析基础小说大纲. 请先生成主要大纲.");
    }

    // 2. Find the specific chapter's data
    Map<String, dynamic>? targetChapterData;
    if (fullOutline.containsKey('chapters') &&
        fullOutline['chapters'] is List) {
      final chaptersList = fullOutline['chapters'] as List;
      targetChapterData = chaptersList.firstWhere(
        (ch) =>
            ch is Map<String, dynamic> && ch['chapterNumber'] == chapterNumber,
        orElse: () => null,
      ) as Map<String, dynamic>?;
    }

    if (targetChapterData == null) {
      throw Exception("在 '$novelTitle' 的已存储大纲中未找到第 $chapterNumber 章.");
    }

    final String chapterTitle =
        targetChapterData['chapterTitle'] as String? ?? '无标题章节 $chapterNumber';
    final String chapterSummary =
        targetChapterData['summary'] as String? ?? '无摘要信息.';

    // 3. Prepare input for DetailedOutlineChain
    String charactersString =
        _buildCharacterDetailsString(characterCards ?? {});
    String writingStylePromptString = writingStyle?.getPrompt() ?? '';
    String knowledgeBaseString = '';
    final knowledgeBaseController = Get.find<KnowledgeBaseController>();
    if (knowledgeBaseController.useKnowledgeBase.value &&
        knowledgeBaseController.selectedDocIds.isNotEmpty) {
      knowledgeBaseString = knowledgeBaseController.getSelectedDocsContent();
    }

    final detailedInput = {
      'novelTitle': novelTitle,
      'genres': genres.join(', '),
      'theme': theme ?? '无主题',
      'targetReaders': targetReaders ?? '通用',
      'background': background ?? '无背景',
      'otherRequirements': otherRequirements ?? '无其他要求',
      'characters': charactersString,
      'writingStylePrompt': writingStylePromptString.isNotEmpty
          ? writingStylePromptString
          : '无特定文风要求',
      'knowledgeBase':
          knowledgeBaseString.isNotEmpty ? knowledgeBaseString : '无知识库信息',
      // --- Specific chapter info ---
      'chapterNumber': chapterNumber.toString(), // Pass as string
      'chapterTitle': chapterTitle,
      'chapterSummary': chapterSummary,
    };

    // 保存用户输入到对话历史
    final userMessage = app_chat.ChatMessage.user(
      content: "生成《$novelTitle》第$chapterNumber章的详细大纲",
      novelTitle: novelTitle,
      chapterNumber: chapterNumber,
    );
    await Get.find<ChatHistoryService>().addMessage(userMessage);

    // 4. Call the DetailedOutlineChain
    try {
      print(
          "[NovelGenerationService.generateDetailedChapterOutline] 调用 DetailedOutlineChain 生成第 $chapterNumber 章的细纲...");
      final String detailedOutlineMarkdown =
          await detailedChain.generateDetailedOutlineForChapter(detailedInput);

      print(
          "[NovelGenerationService.generateDetailedChapterOutline] 第 $chapterNumber 章详细大纲生成成功 (长度: ${detailedOutlineMarkdown.length}).");

      // 保存AI回复到对话历史
      final aiMessage = app_chat.ChatMessage.ai(
        content: "已生成《$novelTitle》第$chapterNumber章的详细大纲",
        novelTitle: novelTitle,
        chapterNumber: chapterNumber,
      );
      await Get.find<ChatHistoryService>().addMessage(aiMessage);

      return detailedOutlineMarkdown.trim(); // Return trimmed markdown
    } catch (e) {
      print(
          "[NovelGenerationService.generateDetailedChapterOutline] 调用 DetailedOutlineChain 时出错: $e");
      rethrow;
    }
  }

  /// 生成章节内容
  Future<String> generateChapter({
    required String novelTitle,
    required int chapterNumber,
    required String chapterTitle,
    required String
        outlineContent, // IMPORTANT: Expects DETAILED MARKDOWN outline
    required List<String> genres,
    required String theme,
    required String targetReaders,
    String? background,
    String? otherRequirements,
    String? previousChapterSummary,
    WritingStylePackage? writingStyle,
    Map<String, CharacterCard>? characterCards,
    void Function(String)? onProgress,
    String? sessionId,
  }) async {
    print(
        "[NovelGenerationService.generateChapter] 为 '$novelTitle', 第 $chapterNumber 章 '$chapterTitle' 生成内容. (使用详细大纲)");
    try {
      // 获取当前模型配置，确保使用正确的API路径
      final modelConfig = _apiConfigController.getCurrentModel();
      print(
          '[NovelGenerationService.generateChapter] 使用模型: ${modelConfig.name}');
      print(
          '[NovelGenerationService.generateChapter] API格式: ${modelConfig.apiFormat}');
      print(
          '[NovelGenerationService.generateChapter] API路径: ${modelConfig.apiPath}');
      print(
          '[NovelGenerationService.generateChapter] 使用代理: ${modelConfig.useProxy}');

      // 如果是Google API，确保路径正确
      if (modelConfig.apiFormat == 'Google API' &&
          !modelConfig.apiPath.contains(':generateContent')) {
        // 自动修正路径
        final correctPath =
            '/v1beta/models/${modelConfig.model}:generateContent';
        print(
            '[NovelGenerationService.generateChapter] 自动修正Google API路径为: $correctPath');
        await _apiConfigController.updateModelConfig(
          modelConfig.name,
          apiPath: correctPath,
        );
      }

      final chain = await _getChain(novelTitle);
      final novelMemory = sessionId != null
          ? NovelMemory(novelTitle: novelTitle, sessionId: sessionId)
          : _getNovelMemory(novelTitle);

      // 获取全部历史内容作为上下文
      String previousChaptersContext = '';
      String fullNovelContext = '';

      try {
        print("[NovelGenerationService.generateChapter] 获取完整的小说上下文...");

        // 获取之前章节的内容
        if (chapterNumber > 1) {
          // 检查是否启用了嵌入模型
          if (_apiConfigController.embeddingModel.value.enabled) {
            try {
              print(
                  "[NovelGenerationService.generateChapter] 使用嵌入模型获取相关章节内容...");

              // 获取当前章节的大纲和细纲
              final chapterOutline = outlineContent;

              // 获取所有已生成的章节
              final allPreviousChapters = await novelMemory.getAllChapters();

              // 将章节转换为文档格式
              final List<Map<String, dynamic>> documents = [];
              allPreviousChapters.forEach((number, content) {
                if (number < chapterNumber) {
                  documents.add({
                    'chapterNumber': number,
                    'text': content,
                  });
                }
              });

              if (documents.isNotEmpty) {
                // 使用嵌入模型查找与当前章节相关的内容
                final similarDocuments = await _embeddingService
                    .findSimilarTexts(chapterOutline, documents,
                        textField: 'text');

                // 构建上下文
                final buffer = StringBuffer();
                buffer.writeln("# 相关章节内容");

                for (final doc in similarDocuments) {
                  final chapterNum = doc['chapterNumber'] as int;
                  final similarity = doc['similarity'] as double;
                  final text = doc['text'] as String;

                  buffer.writeln(
                      "## 第$chapterNum章 (相似度: ${similarity.toStringAsFixed(2)})");
                  // 只取前2000个字符，避免上下文过长
                  buffer.writeln(text.length > 2000
                      ? "${text.substring(0, 2000)}..."
                      : text);
                  buffer.writeln();
                }

                previousChaptersContext = buffer.toString();
                print(
                    "[NovelGenerationService.generateChapter] 成功使用嵌入模型获取相关章节，长度: ${previousChaptersContext.length}");
              } else {
                print("[NovelGenerationService.generateChapter] 没有找到已生成的章节");
              }
            } catch (e) {
              print("[NovelGenerationService.generateChapter] 使用嵌入模型时出错: $e");
              // 如果嵌入模型出错，回退到传统方式
              previousChaptersContext =
                  await novelMemory.getPreviousChapters(chapterNumber);
            }
          } else {
            // 传统方式获取之前章节
            previousChaptersContext =
                await novelMemory.getPreviousChapters(chapterNumber);
          }

          print(
              "[NovelGenerationService.generateChapter] 成功获取之前章节内容，长度: ${previousChaptersContext.length}");

          // 如果没有提供上一章摘要，则自动生成
          if (previousChapterSummary == null ||
              previousChapterSummary.isEmpty) {
            final previousChapterContent =
                await novelMemory.getChapter(chapterNumber - 1);
            if (previousChapterContent != null &&
                previousChapterContent.isNotEmpty) {
              previousChapterSummary =
                  "上一章中，${previousChapterContent.substring(0, min(300, previousChapterContent.length))}...";
              print(
                  "[NovelGenerationService.generateChapter] 自动生成了上一章摘要，长度: ${previousChapterSummary.length}");
            }
          }
        }

        // 获取大纲信息
        final outlineJson = await novelMemory.getOutline();
        if (outlineJson != null && outlineJson.isNotEmpty) {
          print(
              "[NovelGenerationService.generateChapter] 成功获取小说大纲，长度: ${outlineJson.length}");

          // 将大纲信息添加到上下文中
          fullNovelContext =
              "# 小说大纲\n$outlineJson\n\n---\n\n$previousChaptersContext";
        } else {
          fullNovelContext = previousChaptersContext;
        }

        print(
            "[NovelGenerationService.generateChapter] 已准备完整的小说上下文，总长度: ${fullNovelContext.length}");
        // 使用完整上下文替换之前的章节上下文
        previousChaptersContext = fullNovelContext;
      } catch (e) {
        print("[NovelGenerationService.generateChapter] 获取小说上下文时出错: $e");
        // 出错时不中断执行，使用空字符串
        previousChaptersContext = '';
      }

      // Prepare input for chapter generation
      String knowledgeBaseString = '';
      final knowledgeBaseController = Get.find<KnowledgeBaseController>();
      if (knowledgeBaseController.useKnowledgeBase.value &&
          knowledgeBaseController.selectedDocIds.isNotEmpty) {
        knowledgeBaseString = knowledgeBaseController.getSelectedDocsContent();
      }
      String charactersString =
          _buildCharacterDetailsString(characterCards ?? {});

      // 准备章节生成的输入参数
      final input = {
        'novelTitle': novelTitle,
        'genres': genres.join(', '),
        'chapterNumber': chapterNumber.toString(),
        'chapterTitle': chapterTitle,
        'outlineContent': outlineContent, // Pass the detailed markdown outline
        'task': 'generate_chapter',
        'theme': theme ?? '无主题',
        'targetReaders': targetReaders ?? '通用',
        'background': background ?? '无背景',
        'otherRequirements': otherRequirements ?? '无其他要求',
        'previousChapterSummary': previousChapterSummary ?? '无上一章摘要',
        'knowledgeBase':
            knowledgeBaseString.isNotEmpty ? knowledgeBaseString : '无知识库信息',
        'characters': charactersString,
        'writingStylePrompt': writingStyle?.getPrompt() ?? '无特定文风要求',
        // 添加完整的小说上下文作为输入
        'input': "生成第$chapterNumber章：$chapterTitle",
        'history': previousChaptersContext,
      };

      // 打印历史上下文的长度
      print(
          "[NovelGenerationService.generateChapter] 历史上下文长度: ${previousChaptersContext.length}");

      // 确保历史上下文不为空
      if (previousChaptersContext.isEmpty) {
        print("[NovelGenerationService.generateChapter] 警告: 历史上下文为空");
      }

      // --- Detailed Input Logging ---
      print(
          '[NovelGenerationService.generateChapter] --- Input to Chain --- START ---');
      input.forEach((key, value) {
        final valueStr = value.toString();
        final displayValue = valueStr.length > 200
            ? '${valueStr.substring(0, 200)}...'
            : valueStr; // Truncate long values
        print('  [$key]: $displayValue');
      });
      print(
          '[NovelGenerationService.generateChapter] --- Input to Chain --- END ---');
      // --- End Detailed Input Logging ---

      // 保存用户输入到对话历史
      final userMessage = app_chat.ChatMessage.user(
        content: "生成第$chapterNumber章：$chapterTitle",
        novelTitle: novelTitle,
        chapterNumber: chapterNumber,
      );
      await Get.find<ChatHistoryService>().addMessage(userMessage);

      String result;

      // Execute chain (stream or run)
      if (onProgress != null) {
        final resultStream = chain.stream(input);
        final buffer = StringBuffer();
        await for (final chunk in resultStream) {
          buffer.write(chunk);
          onProgress(chunk);
        }
        print("[NovelGenerationService.generateChapter] 章节生成流结束.");
        result = buffer.toString();
      } else {
        print("[NovelGenerationService.generateChapter] 运行章节生成 (非流式)...");
        result = await chain.run(input);
        print(
            "[NovelGenerationService.generateChapter] 章节生成完成 (非流式). 结果长度: ${result.length}");
      }

      // 保存AI回复到对话历史
      final aiMessage = app_chat.ChatMessage.ai(
        content: "已生成第$chapterNumber章：$chapterTitle",
        novelTitle: novelTitle,
        chapterNumber: chapterNumber,
      );
      await Get.find<ChatHistoryService>().addMessage(aiMessage);

      return result;
    } catch (e) {
      print(
          '[NovelGenerationService.generateChapter] 为第 $chapterNumber 章生成章节内容失败: $e');
      rethrow;
    }
  }

  // 续写小说方法已移除，将在后续重新实现

  /// 生成短篇小说大纲
  // 删除重复的方法，使用下面的generateShortNovelPart方法

  /// 生成短篇小说大纲 - 返回JSON格式
  Future<String> generateShortNovelOutline({
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required int wordCount,
    String? background,
    String? otherRequirements,
    WritingStylePackage? writingStyle,
    List<CharacterCard>? characterCards,
  }) async {
    print(
        "[NovelGenerationService.generateShortNovelOutline] 开始为 '$novelTitle' 生成短篇小说大纲...");
    try {
      // 计算需要生成的部分数量
      // 每部分约生成 3000 字
      final int totalParts = (wordCount / 3000).ceil();

      // 构建提示词
      final promptText = "请为短篇小说《$novelTitle》生成一个详细的大纲，并以JSON格式返回。\n\n"
          "小说信息：\n"
          "- 标题：$novelTitle\n"
          "- 类型：${genres.join(', ')}\n"
          "- 主题：$theme\n"
          "- 目标读者：$targetReaders\n"
          "- 字数要求：约$wordCount字\n"
          "- 背景设定：${background ?? '无背景'}\n"
          "- 其他要求：${otherRequirements ?? '无其他要求'}\n"
          "- 写作风格：${writingStyle?.getPrompt() ?? '无特定文风要求'}\n\n"
          "${_buildCharacterDetailsString(characterCards?.asMap().map((key, value) => MapEntry(value.name, value)) ?? {})}\n\n"
          "请生成一个详细的大纲，将故事分为$totalParts个部分，每个部分对应小说的一个进度段落。\n\n"
          "返回格式要求：\n"
          "请以JSON格式返回，格式如下：\n"
          "```json\n"
          "{\n"
          "  \"title\": \"小说标题\",\n"
          "  \"totalParts\": $totalParts,\n"
          "  \"parts\": [\n"
          "    {\n"
          "      \"partNumber\": 1,\n"
          "      \"startPercentage\": 0,\n"
          "      \"endPercentage\": ${(100 / totalParts).round()},\n"
          "      \"summary\": \"第一部分的内容概要...\"\n"
          "    },\n"
          "    {\n"
          "      \"partNumber\": 2,\n"
          "      \"startPercentage\": ${(100 / totalParts).round()},\n"
          "      \"endPercentage\": ${(200 / totalParts).round()},\n"
          "      \"summary\": \"第二部分的内容概要...\"\n"
          "    },\n"
          "    // 以此类推，直到最后一部分\n"
          "  ]\n"
          "}\n"
          "```\n"
          "请确保每个部分的summary字段详细描述该部分的情节发展，包括主要事件、人物互动和情感变化。";

      // 获取当前模型配置
      final modelConfig = _apiConfigController.getCurrentModel();
      final llm = ModelAdapter.createLLMFromConfig(modelConfig);

      // 调用 AI 生成
      final promptValue = ChatPromptTemplate.fromPromptMessages([
        SystemChatMessagePromptTemplate.fromTemplate(
            "You are a helpful assistant that generates novel outlines in JSON format."),
        HumanChatMessagePromptTemplate.fromTemplate("{input}"),
      ]).formatPrompt({"input": promptText});

      final result = await llm.invoke(promptValue);
      String content = result.toString();
      print(
          "[NovelGenerationService.generateShortNovelOutline] 短篇小说大纲生成完成. 结果长度: ${content.length}");

      // 尝试提取JSON部分
      try {
        // 检查是否包含JSON代码块
        final RegExp jsonRegex = RegExp(r'```json\s*([\s\S]*?)\s*```');
        final match = jsonRegex.firstMatch(content);

        if (match != null) {
          // 提取JSON部分
          content = match.group(1)!.trim();
          print(
              "[NovelGenerationService.generateShortNovelOutline] 成功提取JSON部分");
        }

        // 验证JSON格式
        final jsonData = jsonDecode(content);
        // 重新编码为格式化的JSON
        content = jsonEncode(jsonData);
        print("[NovelGenerationService.generateShortNovelOutline] JSON格式有效");
      } catch (jsonError) {
        print(
            "[NovelGenerationService.generateShortNovelOutline] JSON解析失败: $jsonError");
        print(
            "[NovelGenerationService.generateShortNovelOutline] 尝试修复JSON格式...");

        // 尝试提取JSON对象
        final RegExp jsonObjectRegex = RegExp(r'({[\s\S]*})');
        final objectMatch = jsonObjectRegex.firstMatch(content);

        if (objectMatch != null) {
          try {
            final extractedJson = objectMatch.group(1)!;
            final jsonData = jsonDecode(extractedJson);
            content = jsonEncode(jsonData);
            print(
                "[NovelGenerationService.generateShortNovelOutline] JSON修复成功");
          } catch (e) {
            print(
                "[NovelGenerationService.generateShortNovelOutline] JSON修复失败: $e");
            // 如果无法修复，创建一个基本的JSON结构
            final Map<String, dynamic> fallbackJson = {
              "title": novelTitle,
              "totalParts": totalParts,
              "parts": List.generate(
                  totalParts,
                  (i) => {
                        "partNumber": i + 1,
                        "startPercentage": (i * 100 / totalParts).round(),
                        "endPercentage": ((i + 1) * 100 / totalParts).round(),
                        "summary": "第${i + 1}部分的内容概要"
                      })
            };
            content = jsonEncode(fallbackJson);
            print(
                "[NovelGenerationService.generateShortNovelOutline] 使用备用JSON结构");
          }
        }
      }

      return content;
    } catch (e) {
      print(
          '[NovelGenerationService.generateShortNovelOutline] 生成短篇小说大纲失败: $e');
      rethrow;
    }
  }

  /// 生成短篇小说的一部分
  Future<String> generateShortNovelPart({
    required String novelTitle,
    required String outline,
    required int partNumber,
    required int totalParts,
    required String previousContent,
    required int startPercentage,
    required int endPercentage,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required String background,
    required String otherRequirements,
    required String writingStyle,
    required List<CharacterCard> characterCards,
    Function(String)? onProgress,
  }) async {
    print(
        "[NovelGenerationService.generateShortNovelPart] 开始为 '$novelTitle' 生成第 $partNumber/$totalParts 部分...");
    try {
      // 构建提示词
      final prompt =
          "请继续生成短篇小说《$novelTitle》的内容。这是第 $partNumber 部分（共 $totalParts 部分），对应故事进度的 $startPercentage% 到 $endPercentage%。\n\n"
          "小说信息：\n"
          "- 标题：$novelTitle\n"
          "- 类型：${genres.join(', ')}\n"
          "- 主题：$theme\n"
          "- 目标读者：$targetReaders\n"
          "- 背景设定：$background\n"
          "- 其他要求：$otherRequirements\n"
          "- 写作风格：$writingStyle\n\n"
          "小说大纲：\n$outline\n\n"
          "${_buildCharacterDetailsString(characterCards.asMap().map((key, value) => MapEntry(value.name, value)))}\n\n"
          "${previousContent.isNotEmpty ? '前文内容（请无缝衔接）：\n$previousContent\n\n' : ''}"
          "创作要求：\n"
          "1. 请用非常简洁的描述方式描述剧情，冲突部分可以详细描写\n"
          "2. 快节奏，多对话形式，以小见大\n"
          "3. 人物对话格式：'对话内容'某某说道\n"
          "4. 严禁使用任何形式的小标题、序号或章节编号\n"
          "5. 严禁使用情节点、转折点、高潮点等标题或分段标记\n"
          "6. 严禁使用总结性语言，如\"总之\"、\"总的来说\"、\"简而言之\"等\n"
          "7. 严禁添加旁白或解说，严禁添加\"作者注\"、\"编者按\"等内容\n"
          "8. 直接用流畅的叙述展开故事，只关注推动情节发展的内容\n"
          "9. 如果这是第一部分，请从故事开头开始写起；如果是中间部分，请无缝衔接前文内容；如果是最后部分，请确保故事有一个完整的结局\n"
          "10. 这部分内容字数控制在2100-3000字之间\n\n"
          "请直接生成内容，不要添加任何标题或前言。";

      // 获取当前模型配置
      final modelConfig = _apiConfigController.getCurrentModel();
      final llm = ModelAdapter.createLLMFromConfig(modelConfig);

      // 调用 AI 生成
      String response;
      if (onProgress != null) {
        // 创建提示词
        final promptValue = ChatPromptTemplate.fromPromptMessages([
          SystemChatMessagePromptTemplate.fromTemplate(
              "You are a helpful assistant that generates novel content."),
          HumanChatMessagePromptTemplate.fromTemplate("{input}"),
        ]).formatPrompt({"input": prompt});

        final resultStream = llm.stream(promptValue);
        final buffer = StringBuffer();
        await for (final chunk in resultStream) {
          final textChunk = chunk.toString();
          buffer.write(textChunk);
          onProgress(textChunk);
        }
        response = buffer.toString();
        print(
            "[NovelGenerationService.generateShortNovelPart] 短篇小说部分生成完成 (流式). 结果长度: ${response.length}");
      } else {
        // 创建提示词
        final promptValue = ChatPromptTemplate.fromPromptMessages([
          SystemChatMessagePromptTemplate.fromTemplate(
              "You are a helpful assistant that generates novel content."),
          HumanChatMessagePromptTemplate.fromTemplate("{input}"),
        ]).formatPrompt({"input": prompt});

        final result = await llm.invoke(promptValue);
        response = result.toString();
        print(
            "[NovelGenerationService.generateShortNovelPart] 短篇小说部分生成完成 (非流式). 结果长度: ${response.length}");
      }

      return response;
    } catch (e) {
      print('[NovelGenerationService.generateShortNovelPart] 生成短篇小说部分失败: $e');
      rethrow;
    }
  }

  // 缓存短篇小说生成链
  final Map<String, NovelGenerationChain> _shortNovelChainCache = {};

  /// 初始化短篇小说生成链，并保存大纲到NovelMemory
  Future<void> initializeShortNovelChain({
    required String novelTitle,
    required String outline,
  }) async {
    print(
        "[NovelGenerationService.initializeShortNovelChain] 初始化短篇小说生成链: '$novelTitle'");
    try {
      // 获取当前模型配置
      final modelConfig = _apiConfigController.getCurrentModel();
      final llm = ModelAdapter.createLLMFromConfig(modelConfig);

      // 创建新的生成链
      final chain = NovelGenerationChain(
        llm: llm,
        novelTitle: novelTitle,
      );

      // 缓存生成链
      _shortNovelChainCache[novelTitle] = chain;

      // 保存大纲到NovelMemory
      final novelMemory = _getNovelMemory(novelTitle);
      await novelMemory.saveOutline(outline);

      print(
          "[NovelGenerationService.initializeShortNovelChain] 短篇小说生成链初始化完成，大纲已保存到NovelMemory");
    } catch (e) {
      print(
          '[NovelGenerationService.initializeShortNovelChain] 初始化短篇小说生成链失败: $e');
      rethrow;
    }
  }

  /// 使用LangChain的memory功能生成短篇小说的一部分
  Future<String> generateShortNovelPartWithMemory({
    required String novelTitle,
    required int partNumber,
    required int totalParts,
    required int startPercentage,
    required int endPercentage,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required String background,
    required String otherRequirements,
    required String writingStyle,
    required List<CharacterCard> characterCards,
    Function(String)? onProgress,
  }) async {
    print(
        "[NovelGenerationService.generateShortNovelPartWithMemory] 开始为 '$novelTitle' 生成第 $partNumber/$totalParts 部分...");

    // 确保嵌入模型在短篇小说生成时被禁用
    final originalEmbeddingEnabled =
        _apiConfigController.embeddingModel.value.enabled;
    if (originalEmbeddingEnabled) {
      print("[NovelGenerationService] 短篇小说生成时禁用嵌入模型");
      // 临时禁用嵌入模型
      _apiConfigController.updateEmbeddingModelEnabled(false);
    }

    // 打印所有参数
    print("[DEBUG] 参数详情:");
    print("[DEBUG] novelTitle: $novelTitle");
    print("[DEBUG] partNumber: $partNumber");
    print("[DEBUG] totalParts: $totalParts");
    print("[DEBUG] startPercentage: $startPercentage");
    print("[DEBUG] endPercentage: $endPercentage");
    print("[DEBUG] genres: ${genres.join(', ')}");
    print("[DEBUG] theme: $theme");
    print("[DEBUG] targetReaders: $targetReaders");
    print("[DEBUG] background: $background");
    print("[DEBUG] otherRequirements: $otherRequirements");
    print("[DEBUG] writingStyle: $writingStyle");
    print("[DEBUG] characterCards.length: ${characterCards.length}");
    if (characterCards.isNotEmpty) {
      print("[DEBUG] 第一个角色: ${characterCards.first.name}");
    }
    print("[DEBUG] onProgress是否为null: ${onProgress == null}");
    print(
        "[DEBUG] 嵌入模型已禁用: ${!_apiConfigController.embeddingModel.value.enabled}");

    try {
      // 获取生成链
      NovelGenerationChain? chain = _shortNovelChainCache[novelTitle];
      if (chain == null) {
        print("[DEBUG] 创建新的生成链");
        // 如果生成链不存在，创建新的生成链
        final modelConfig = _apiConfigController.getCurrentModel();
        print("[DEBUG] 使用模型: ${modelConfig.name}");
        final llm = ModelAdapter.createLLMFromConfig(modelConfig);
        chain = NovelGenerationChain(
          llm: llm,
          novelTitle: novelTitle,
        );
        _shortNovelChainCache[novelTitle] = chain;
      } else {
        print("[DEBUG] 使用缓存的生成链");
      }

      // 获取大纲和之前生成的内容
      final novelMemory = _getNovelMemory(novelTitle);
      final outline = await novelMemory.getOutline() ?? '';
      print("[DEBUG] 大纲长度: ${outline.length}");

      // 获取之前生成的所有部分内容
      String previousContent = '';
      if (partNumber > 1) {
        print("[DEBUG] 获取之前部分的内容");
        // 获取之前所有部分的内容
        for (int i = 1; i < partNumber; i++) {
          final partContent = await novelMemory.getChapter(i);
          print("[DEBUG] 第$i部分内容长度: ${partContent?.length ?? 0}");
          if (partContent != null && partContent.isNotEmpty) {
            previousContent += partContent + '\n\n';
          }
        }
      }
      print("[DEBUG] 前文内容总长度: ${previousContent.length}");

      // 构建角色详情字符串
      String characterDetailsString;
      try {
        characterDetailsString = _buildCharacterDetailsString(characterCards);
        print("[DEBUG] 角色详情字符串长度: ${characterDetailsString.length}");
      } catch (e) {
        print("[DEBUG] 构建角色详情字符串失败: $e");
        characterDetailsString = "无角色设定";
      }

      // 使用NovelPromptTemplates中的shortNovelTemplate作为基础，但需要适应分部分生成的情况
      // 增强大纲解析和提取逻辑
      String currentPartSummary = '';
      bool outlineFound = false;

      print("[DEBUG] 开始解析大纲，查找第$partNumber部分的概要");
      print(
          "[DEBUG] 大纲内容预览: ${outline.length > 200 ? outline.substring(0, 200) + '...' : outline}");

      try {
        if (outline.isNotEmpty) {
          // 尝试解析JSON格式大纲
          try {
            final outlineJson = jsonDecode(outline);
            print("[DEBUG] 成功解析大纲JSON，键: ${outlineJson.keys}");

            if (outlineJson.containsKey('parts') &&
                outlineJson['parts'] is List) {
              final parts = outlineJson['parts'] as List;
              print("[DEBUG] 大纲包含${parts.length}个部分");

              // 首先尝试精确匹配部分号
              for (int i = 0; i < parts.length; i++) {
                final part = parts[i];
                if (part is Map) {
                  final partNum = part['partNumber'];
                  print("[DEBUG] 检查第${i + 1}个部分，partNumber: $partNum");

                  if (partNum != null &&
                      (partNum == partNumber ||
                          partNum.toString() == partNumber.toString())) {
                    currentPartSummary = part['summary'] as String? ?? '无概要';
                    print("[DEBUG] ✓ 找到精确匹配的部分大纲概要: $currentPartSummary");
                    outlineFound = true;
                    break;
                  }
                }
              }

              // 如果精确匹配失败，尝试按索引匹配（partNumber可能从0开始或有其他问题）
              if (!outlineFound && partNumber <= parts.length) {
                final part = parts[partNumber - 1]; // 数组索引从0开始
                if (part is Map && part.containsKey('summary')) {
                  currentPartSummary = part['summary'] as String? ?? '无概要';
                  print("[DEBUG] ✓ 通过索引找到部分大纲概要: $currentPartSummary");
                  outlineFound = true;
                }
              }

              // 如果还是没找到，尝试百分比范围匹配
              if (!outlineFound) {
                print(
                    "[DEBUG] 尝试百分比范围匹配，目标范围: $startPercentage% - $endPercentage%");
                for (final part in parts) {
                  if (part is Map &&
                      part.containsKey('startPercentage') &&
                      part.containsKey('endPercentage')) {
                    final partStartPercentage = part['startPercentage'] is int
                        ? part['startPercentage'] as int
                        : int.tryParse(part['startPercentage'].toString()) ??
                            -1;

                    final partEndPercentage = part['endPercentage'] is int
                        ? part['endPercentage'] as int
                        : int.tryParse(part['endPercentage'].toString()) ?? -1;

                    print(
                        "[DEBUG] 检查部分百分比范围: $partStartPercentage% - $partEndPercentage%");

                    if (partStartPercentage <= startPercentage &&
                        partEndPercentage >= endPercentage) {
                      currentPartSummary = part['summary'] as String? ?? '无概要';
                      print("[DEBUG] ✓ 通过百分比范围找到部分大纲概要: $currentPartSummary");
                      outlineFound = true;
                      break;
                    }
                  }
                }
              }
            }
          } catch (jsonError) {
            print("[DEBUG] JSON解析失败，尝试使用正则表达式提取: $jsonError");

            // 如果JSON解析失败，尝试使用正则表达式提取
            final partPattern = RegExp(
                r'第\s*' +
                    partNumber.toString() +
                    r'\s*部分.*?[:：]\s*(.*?)(?=第\s*\d+\s*部分|$)',
                dotAll: true);
            final match = partPattern.firstMatch(outline);

            if (match != null && match.groupCount >= 1) {
              currentPartSummary = match.group(1)?.trim() ?? '';
              print("[DEBUG] ✓ 使用正则表达式找到部分大纲概要: $currentPartSummary");
              outlineFound = true;
            }
          }
        }
      } catch (e) {
        print("[DEBUG] 解析大纲失败: $e");
      }

      // 如果仍然没有找到大纲，使用默认值
      if (currentPartSummary.isEmpty) {
        currentPartSummary =
            "第$partNumber部分内容 ($startPercentage% - $endPercentage%)，请根据整体故事发展生成相应内容";
        print("[DEBUG] ⚠ 使用默认部分概要: $currentPartSummary");
      } else {
        print("[DEBUG] ✓ 最终使用的部分概要: $currentPartSummary");
      }

      // 提取系统提示词，避免重复传递
      String systemPrompt = '';
      // 检查otherRequirements是否包含系统提示词
      if (otherRequirements.contains("一篇优秀的短篇网文") &&
          otherRequirements.contains("引人入胜的开端") &&
          otherRequirements.contains("精炼的展开与激化")) {
        // 系统提示词已包含在otherRequirements中，不需要额外添加
        systemPrompt = otherRequirements;
        print("[DEBUG] 系统提示词已包含在otherRequirements中");
      } else {
        // 获取系统提示词
        systemPrompt = otherRequirements;
        print("[DEBUG] 使用原始otherRequirements作为系统提示词");
      }

      // 构建强调大纲重要性和前文连贯性的提示词
      String contextPrompt = "";
      if (previousContent.isNotEmpty) {
        // 获取前文的最后500字作为直接上下文
        final lastContext = previousContent.length > 500
            ? previousContent.substring(previousContent.length - 500)
            : previousContent;
        contextPrompt = "\n\n【前文内容摘要】（请确保内容连贯）：\n...${lastContext.trim()}\n";
      }

      final prompt =
          "请继续生成短篇小说《$novelTitle》的内容。这是第 $partNumber 部分（共 $totalParts 部分），对应故事进度的 $startPercentage% 到 $endPercentage%。\n\n"
          "【关键要求】：\n"
          "1. 严格按照以下大纲概要生成内容，不得偏离情节走向\n"
          "2. 确保与前文内容逻辑连贯，人物性格一致\n"
          "3. 生成约3000字的内容\n\n"
          "【当前部分大纲概要】：\n$currentPartSummary\n"
          "$contextPrompt\n"
          "请将大纲中的情节以生动、详细的方式展现，确保故事发展符合大纲设定。直接开始生成内容，不要添加标题或前言。";

      print("[DEBUG] 提示词长度: ${prompt.length}");

      // 准备输入 - 将前文内容和大纲信息都传递给AI
      final input = {
        'input': prompt,
        'novelTitle': novelTitle,
        'partNumber': partNumber.toString(),
        'totalParts': totalParts.toString(),
        'task': 'generate_short_novel_part',
        'genres': genres,
        'theme': theme,
        'targetReaders': targetReaders,
        'background': background,
        'otherRequirements': systemPrompt, // 使用系统提示词，避免重复
        'writingStyle': writingStyle,
        'characters': characterDetailsString, // 直接使用已构建的角色详情字符串
        'history': previousContent, // 通过history参数传递前文内容
        'outline': outline, // 传递完整大纲信息
        'currentPartSummary': currentPartSummary, // 传递当前部分概要
        'wordCount': '3000', // 添加目标字数
        'startPercentage': startPercentage.toString(),
        'endPercentage': endPercentage.toString(),
      };

      print("[DEBUG] 前文内容长度: ${previousContent.length}字");

      print("[DEBUG] 输入参数: ${input.keys.join(', ')}");

      // 执行生成
      String response;
      if (onProgress != null) {
        try {
          print("[DEBUG] 开始流式生成");
          final resultStream = chain.stream(input);
          final buffer = StringBuffer();
          int chunkCount = 0;

          await for (final chunk in resultStream) {
            chunkCount++;
            // 确保 chunk 是字符串，并处理可能的 ChatMessage 格式
            String safeChunk;
            try {
              // 尝试获取 contentAsString 属性
              final dynamic dynamicChunk = chunk;

              if (dynamicChunk != null) {
                try {
                  // 尝试直接访问 contentAsString 属性
                  safeChunk = dynamicChunk.contentAsString;
                } catch (_) {
                  // 如果没有 contentAsString 属性，使用 toString()
                  safeChunk = dynamicChunk.toString();

                  // 如果内容包含 ChatMessage 类名，尝试清理
                  if (safeChunk.contains('ChatMessage')) {
                    // 尝试提取 content 字段
                    final contentMatch =
                        RegExp(r'content:\s*(.*?),').firstMatch(safeChunk);
                    if (contentMatch != null && contentMatch.groupCount >= 1) {
                      safeChunk = contentMatch.group(1) ?? safeChunk;
                    }
                  }
                }
              } else {
                safeChunk = "";
              }

              print("[DEBUG] 收到第$chunkCount个内容块，长度: ${safeChunk.length}");
            } catch (e) {
              print("[DEBUG] 转换内容块为字符串失败: $e");
              safeChunk = "";
            }

            if (safeChunk.isNotEmpty) {
              buffer.write(safeChunk);
              try {
                onProgress(safeChunk);
              } catch (e) {
                print("[DEBUG] 调用onProgress回调失败: $e");
              }
            } else {
              print("[DEBUG] 收到空内容块");
            }
          }

          response = buffer.toString();
          print("[DEBUG] 流式生成完成，总共收到$chunkCount个内容块");
          print(
              "[NovelGenerationService.generateShortNovelPartWithMemory] 短篇小说部分生成完成 (流式). 结果长度: ${response.length}");
        } catch (e, stackTrace) {
          print(
              "[NovelGenerationService.generateShortNovelPartWithMemory] 流式生成出错: $e");
          print("[DEBUG] 错误堆栈: $stackTrace");

          // 如果流式生成失败，回退到非流式生成
          print("[DEBUG] 尝试回退到非流式生成");
          try {
            response = await chain.run(input);
            print(
                "[NovelGenerationService.generateShortNovelPartWithMemory] 回退到非流式生成. 结果长度: ${response.length}");
          } catch (runError, runStackTrace) {
            print("[DEBUG] 非流式生成也失败: $runError");
            print("[DEBUG] 非流式生成错误堆栈: $runStackTrace");
            rethrow;
          }
        }
      } else {
        try {
          print("[DEBUG] 开始非流式生成");
          response = await chain.run(input);
          print(
              "[NovelGenerationService.generateShortNovelPartWithMemory] 短篇小说部分生成完成 (非流式). 结果长度: ${response.length}");
        } catch (e, stackTrace) {
          print("[DEBUG] 非流式生成失败: $e");
          print("[DEBUG] 错误堆栈: $stackTrace");
          rethrow;
        }
      }

      // 保存当前部分到NovelMemory - 注意这里不使用"第X章"的格式，而是使用"第X部分"
      try {
        print("[DEBUG] 保存生成内容到NovelMemory");
        await novelMemory.saveChapter(partNumber, "第$partNumber部分", response);
        print("[DEBUG] 保存成功");
      } catch (e) {
        print("[DEBUG] 保存到NovelMemory失败: $e");
        // 继续执行，不要因为保存失败而中断整个流程
      }

      return response;
    } catch (e, stackTrace) {
      print(
          '[NovelGenerationService.generateShortNovelPartWithMemory] 生成短篇小说部分失败: $e');
      print("[DEBUG] 完整错误堆栈: $stackTrace");
      rethrow;
    } finally {
      // 恢复嵌入模型的原始状态
      if (originalEmbeddingEnabled) {
        print("[NovelGenerationService] 恢复嵌入模型的原始状态");
        _apiConfigController.updateEmbeddingModelEnabled(true);
      }
    }
  }

  /// 将完整的短篇小说内容保存到NovelMemory
  Future<void> saveShortNovelToMemory({
    required String novelTitle,
    required String content,
  }) async {
    print(
        "[NovelGenerationService.saveShortNovelToMemory] 保存短篇小说内容到NovelMemory: '$novelTitle'");
    try {
      final novelMemory = _getNovelMemory(novelTitle, sessionId: null);
      await novelMemory.saveShortNovel(content);
      print(
          "[NovelGenerationService.saveShortNovelToMemory] 短篇小说内容已保存到NovelMemory");
    } catch (e) {
      print('[NovelGenerationService.saveShortNovelToMemory] 保存短篇小说内容失败: $e');
      rethrow;
    }
  }

  /// 生成短篇小说 (原方法保留作兼容)
  Future<String> generateShortNovel({
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required int wordCount,
    String? background,
    String? otherRequirements,
    Map<String, CharacterCard>? characterCards,
    WritingStylePackage? writingStyle,
    void Function(String)? onProgress,
  }) async {
    print(
        "[NovelGenerationService.generateShortNovel] 开始为 '$novelTitle' 生成短篇小说...");

    // 确保嵌入模型在短篇小说生成时被禁用
    final originalEmbeddingEnabled =
        _apiConfigController.embeddingModel.value.enabled;
    if (originalEmbeddingEnabled) {
      print("[NovelGenerationService] 短篇小说生成时禁用嵌入模型");
      // 临时禁用嵌入模型
      await _apiConfigController.updateEmbeddingModelEnabled(false);
    }

    try {
      final chain = await _getChain(novelTitle);

      // Prepare input
      String knowledgeBaseString = '';
      final knowledgeBaseController = Get.find<KnowledgeBaseController>();
      if (knowledgeBaseController.useKnowledgeBase.value &&
          knowledgeBaseController.selectedDocIds.isNotEmpty) {
        knowledgeBaseString = knowledgeBaseController.getSelectedDocsContent();
      }
      String charactersString =
          _buildCharacterDetailsString(characterCards ?? {});

      final input = {
        'novelTitle': novelTitle,
        'genres': genres.join(', '),
        'theme': theme,
        'targetReaders': targetReaders,
        'wordCount': wordCount.toString(),
        'background': background ?? '无背景',
        'otherRequirements': otherRequirements ?? '无其他要求',
        'characters': charactersString,
        'writingStylePrompt': writingStyle?.getPrompt() ?? '无特定文风要求',
        'knowledgeBase':
            knowledgeBaseString.isNotEmpty ? knowledgeBaseString : '无知识库信息',
        'task': 'generate_short_novel'
      };

      print(
          "[NovelGenerationService.generateShortNovel] 输入键: ${input.keys.join(', ')}");

      // Execute
      if (onProgress != null) {
        final resultStream = chain.stream(input);
        final buffer = StringBuffer();
        await for (final chunk in resultStream) {
          // 处理可能的 ChatMessage 格式
          String safeChunk;
          try {
            // 尝试获取 contentAsString 属性
            final dynamic dynamicChunk = chunk;

            if (dynamicChunk != null) {
              try {
                // 尝试直接访问 contentAsString 属性
                safeChunk = dynamicChunk.contentAsString;
              } catch (_) {
                // 如果没有 contentAsString 属性，使用 toString()
                safeChunk = dynamicChunk.toString();

                // 如果内容包含 ChatMessage 类名，尝试清理
                if (safeChunk.contains('ChatMessage')) {
                  // 尝试提取 content 字段
                  final contentMatch =
                      RegExp(r'content:\s*(.*?),').firstMatch(safeChunk);
                  if (contentMatch != null && contentMatch.groupCount >= 1) {
                    safeChunk = contentMatch.group(1) ?? safeChunk;
                  }
                }
              }
            } else {
              safeChunk = "";
            }
          } catch (e) {
            print("[DEBUG] 转换内容块为字符串失败: $e");
            safeChunk = "";
          }

          if (safeChunk.isNotEmpty) {
            buffer.write(safeChunk);
            onProgress(safeChunk);
          }
        }
        print("[NovelGenerationService.generateShortNovel] 短篇小说流结束.");
        return buffer.toString();
      } else {
        print("[NovelGenerationService.generateShortNovel] 运行短篇小说生成 (非流式)...");
        final result = await chain.run(input);
        print(
            "[NovelGenerationService.generateShortNovel] 短篇小说生成完成. 结果长度: ${result.length}");
        return result;
      }
    } catch (e) {
      print('[NovelGenerationService.generateShortNovel] 生成短篇小说失败: $e');
      rethrow;
    } finally {
      // 恢复嵌入模型的原始状态
      if (originalEmbeddingEnabled) {
        print("[NovelGenerationService] 恢复嵌入模型的原始状态");
        await _apiConfigController.updateEmbeddingModelEnabled(true);
      }
    }
  }
}
