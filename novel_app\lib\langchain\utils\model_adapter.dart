import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import 'package:langchain/langchain.dart';
import 'package:langchain_openai/langchain_openai.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/adapters/aliyun_qwen_adapter.dart';

/// 模型适配器工具，将应用API配置转换为LangChain LLM模型
class ModelAdapter {
  /// 创建使用代理的HTTP客户端
  static http.Client _createProxyHttpClient(String proxyUrl, Duration timeout) {
    print('创建使用代理的HTTP客户端: $proxyUrl');

    // 解析代理地址和端口
    final parts = proxyUrl.split(':');
    if (parts.length != 2) {
      throw Exception('代理地址格式错误，应为 host:port，如 127.0.0.1:7890');
    }

    final proxyHost = parts[0];
    final proxyPort = int.tryParse(parts[1]);
    if (proxyPort == null) {
      throw Exception('代理端口格式错误: ${parts[1]}');
    }

    // 创建一个新的HttpClient并设置代理
    final httpClient = HttpClient();
    httpClient.findProxy = (uri) => 'PROXY $proxyHost:$proxyPort';
    httpClient.connectionTimeout = timeout;

    // 将HttpClient包装为http.Client
    return IOClient(httpClient);
  }

  /// 根据API配置创建合适的LLM实例
  static BaseChatModel createLLMFromConfig(ModelConfig config) {
    // Add logging here
    print('--- ModelAdapter createLLMFromConfig ---');
    print('Received config name: ${config.name}');
    print('Received config apiUrl: ${config.apiUrl}');
    print('Received config apiPath: ${config.apiPath}');
    print('Received config apiFormat: ${config.apiFormat}');
    print('Received config model: ${config.model}');
    print('--------------------------------------');

    // Default options from config
    final defaultOptions = ChatOpenAIOptions(
      model: config.model,
      temperature: config.temperature,
      maxTokens: config.maxTokens > 0
          ? config.maxTokens
          : null, // Pass null if maxTokens is not set or invalid
      topP: config.topP,
      // Note: repetitionPenalty in ModelConfig might map to frequencyPenalty or presencePenalty.
      // Check LangChain documentation for the best mapping. Using presencePenalty as an example:
      // presencePenalty: config.repetitionPenalty,
    );

    // 检查API格式
    if (config.apiFormat == 'Google API') {
      // Google API（Gemini）
      print('Google API detected, using special handling');
      print('API Path: ${config.apiPath}');
      print('Use Proxy: ${config.useProxy}');
      print('Proxy URL: ${config.proxyUrl}');

      // 确保路径中包含:generateContent
      if (!config.apiPath.contains(':generateContent')) {
        throw Exception(
            '路径错误：Google API路径必须包含:generateContent。正确格式应为/v1beta/models/{model}:generateContent');
      }

      // 构建完整的基础URL
      String baseUrl = config.apiUrl;

      // 如果使用代理，输出提示信息
      if (config.useProxy && config.proxyUrl.isNotEmpty) {
        print('警告：LangChain不支持直接设置代理，请在系统环境中设置 HTTP_PROXY 环境变量');
        print(
            '建议设置：HTTP_PROXY=${config.proxyUrl} HTTPS_PROXY=${config.proxyUrl}');
      }

      // 使用ChatOpenAI类，但需要特殊配置
      // 关键是设置正确的baseUrl和查询参数
      // 注意：我们需要将apiPath中的:generateContent替换为/generateContent
      // 因为LangChain会自动将/chat/completions附加到路径中

      // 处理路径格式
      String correctPath = config.apiPath;
      if (correctPath.contains(':generateContent')) {
        // 将:generateContent替换为/generateContent
        correctPath =
            correctPath.replaceAll(':generateContent', '/generateContent');
        print('已将路径中的:generateContent替换为/generateContent，新路径: $correctPath');
      }

      // 构建完整的URL
      String fullUrl = baseUrl + correctPath;
      print('使用完整URL: $fullUrl');

      // 设置查询参数
      Map<String, String> queryParams = {'key': config.apiKey};

      return ChatOpenAI(
        apiKey: 'not_used', // 不使用标准的apiKey字段，而是使用查询参数
        baseUrl: fullUrl,
        defaultOptions: defaultOptions,
        queryParams: queryParams,
      );
    } else if (config.appId.isNotEmpty) {
      // 百度千帆API
      // Assuming Qianfan also uses OpenAI compatible structure but needs specific headers
      // Check if LangChain has a specific Baidu Qianfan integration
      // Also ensure the baseUrl is correctly set for Qianfan's compatible endpoint
      // The default Qianfan config might need adjustment in ApiConfigController
      return ChatOpenAI(
        apiKey: config
            .apiKey, // Qianfan often uses Secret Key or other auth methods
        baseUrl: config
            .apiUrl, // Ensure this points to the correct base for chat completions endpoint
        defaultOptions: defaultOptions,
        headers: {
          // Adjust headers based on Qianfan documentation if needed
          // e.g., 'Authorization': 'Bearer ${config.apiKey}', // Example, verify correct auth method
        },
        // Qianfan might require query parameters, potentially involving access tokens
        // queryParams: { ... }
      );
    } else {
      // 通用OpenAI兼容API（包括通义千问、Deepseek等 using compatible mode）
      String effectiveBaseUrl = config.apiUrl;

      // Handle specific API path logic if apiPath is provided for compatible modes
      // This logic assumes ChatOpenAI will append '/chat/completions' or similar
      if (config.apiPath.isNotEmpty) {
        const String chatCompletionsSuffix =
            '/chat/completions'; // Common suffix
        if (config.apiPath.endsWith(chatCompletionsSuffix)) {
          // Construct the base URL by combining apiUrl and the path prefix before '/chat/completions'
          String pathPrefix = config.apiPath.substring(
              0, config.apiPath.length - chatCompletionsSuffix.length);

          // Ensure apiUrl doesn't end with '/' and pathPrefix doesn't start with '/' or is empty
          String trimmedApiUrl = config.apiUrl.endsWith('/')
              ? config.apiUrl.substring(0, config.apiUrl.length - 1)
              : config.apiUrl;
          String trimmedPathPrefix = pathPrefix.isEmpty
              ? '' // No prefix if path was just '/chat/completions'
              : (pathPrefix.startsWith('/') ? pathPrefix : '/$pathPrefix');

          effectiveBaseUrl = '$trimmedApiUrl$trimmedPathPrefix';
          // Debug print to verify the constructed base URL
          // print('Using effectiveBaseUrl for compatible API: $effectiveBaseUrl (from apiUrl: ${config.apiUrl}, apiPath: ${config.apiPath})');
        }
        // Add more conditions here if other specific non-standard apiPaths need handling differently
        // else {
        //   // If apiPath doesn't end with the expected suffix, treat apiUrl as the base
        //   effectiveBaseUrl = config.apiUrl;
        // }
      }
      // If apiPath is empty, we use config.apiUrl directly as the base.
      // This works for standard OpenAI (apiUrl=https://api.openai.com)
      // but will likely fail for non-compatible endpoints like ".../generation"
      // entered directly into apiUrl without a corresponding apiPath.

      // 检查是否是阿里云通义千问模型，如果是，使用专门的适配器
      bool isAliyunQwen = config.apiUrl.contains('dashscope.aliyuncs.com') ||
          config.name.contains('阿里') ||
          config.name.contains('通义');

      if (isAliyunQwen) {
        print('[ModelAdapter] 检测到阿里云通义千问模型，使用专门的AliyunQwenAdapter');

        // 使用专门的阿里云通义千问适配器
        final aliyunAdapter = AliyunQwenAdapter(
          apiKey: config.apiKey,
          baseUrl: effectiveBaseUrl,
          model: config.model,
          temperature: config.temperature,
          maxTokens: config.maxTokens > 0 ? config.maxTokens : null,
          topP: config.topP,
          extraHeaders: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
          },
          enableThinking: config.enableThinking, // 使用配置中的深度思考模式设置
        );

        // 将AliyunQwenAdapter转换为BaseChatModel类型
        return aliyunAdapter as BaseChatModel;
      } else {
        return ChatOpenAI(
          apiKey: config.apiKey,
          baseUrl: effectiveBaseUrl,
          defaultOptions: defaultOptions,
        );
      }
    }
  }
}
