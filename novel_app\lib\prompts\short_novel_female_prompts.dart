class ShortNovelFemalePrompts {
  /// 女性向短篇小说基本原则
  static String get basicPrinciples => '''
在创作女性向短篇小说时，请遵循以下核心原则：

1. 情感节奏：
   - 开篇2000字内建立感情基调和氛围
   - 情感转折点设置在总字数的35%和65%处
   - 保持7:3的心理描写与外部行动比例
   - 结局预留15-20%篇幅进行情感余韵和思考空间

2. 人物塑造：
   - 主角需有明确的情感习惯和应对方式（如紧张时整理头发）
   - 描绘微妙的表情变化和肢体语言
   - 配角应有独特的情感能力或缺陷
   - 感情对象应在前三分之一篇幅初步展现魅力点

3. 关系网络：
   - 构建多层次的人际关系
   - 设计2-3组平行关系作为参照和映射
   - 通过日常互动展现关系动态变化
   - 安排关键的情感支持或背叛场景

4. 环境与氛围：
   - 环境描写需呼应人物情感状态
   - 感官描写需强调触觉和嗅觉细节
   - 光影变化作为情绪转变的线索
   - 设计1-2处具有象征意义的场景标志物
''';

  /// 女性向短篇小说的类型特点
  static const Map<String, String> genreCharacteristics = {
    '都市言情': '现代都市背景下的情感故事，聚焦职场与生活平衡，展现都市人情感困境与成长',
    '古代言情': '古代背景下的情感故事，关注阶级与礼教下的自由与束缚，情感与责任的平衡',
    '青春校园': '校园环境中的情感萌发与成长，着重心理变化和自我认知的过程',
    '甜宠': '轻松甜蜜的情感故事，重点在于互动的温馨与日常的浪漫，冲突温和',
    '虐恋': '情感曲折、经历考验的故事，聚焦深沉情感和成长蜕变，有强烈共情',
    '治愈': '温暖疗愈的情感关系，重视心灵的静默交流和彼此的理解与支持',
    '悬疑爱情': '融合感情与谜题，通过解谜过程加深情感理解与发展',
    '职场爱情': '专业领域中的情感发展，平衡事业与情感，展现多元成功的可能',
  };

  /// 女性向短篇小说的写作风格
  static const Map<String, String> writingStyles = {
    '温婉细腻': '柔和的情感表达，细致入微的心理描写，温柔的叙事节奏',
    '清新明快': '明亮的感官描写，简洁流畅的节奏，积极向上的情感走向',
    '深情内敛': '克制的外部表达，丰富的内心世界，含蓄而有力的情感刻画',
    '浪漫唯美': '优美的环境渲染，富有诗意的语言，理想化的情感表达',
    '清冷淡雅': '简约克制的文风，留白的艺术，意蕴深远的象征暗示',
    '幽默风趣': '轻松活泼的对话，诙谐幽默的互动，温暖人心的情感基调',
    '强烈深沉': '大胆直接的情感表达，鲜明的情感对比，强烈的共情力量',
  };

  /// 女性向短篇小说的情感发展模式
  static const List<String> emotionalPatterns = [
    '相遇-试探-误解-理解-相守',
    '初识-吸引-抵触-靠近-融合',
    '依赖-独立-失去-成长-重逢',
    '旧情-疏离-重新认识-治愈-新生',
    '契约-假象-真情-考验-坦诚',
    '对立-冲突-理解-妥协-平衡',
  ];

  /// 女性向短篇小说场景调控建议
  static String get sceneControlSuggestions => '''
场景布局建议：

1. 情感交流场景（占比30%）：
   - 细致捕捉表情与微动作
   - 通过对视、触碰等关键互动传递情感
   - 留意角色间的距离变化和空间位置象征

2. 内心独白场景（占比25%）：
   - 将思考与周围环境细节相结合
   - 通过细微的外部反应展现内心波动
   - 适当使用意象和象征物传达情感

3. 日常互动场景（占比20%）：
   - 设置能展现关系特质的日常活动
   - 通过琐事处理方式反映性格差异
   - 在平凡中营造珍贵的情感时刻

4. 危机/转折场景（占比15%）：
   - 设计情感认知的关键时刻
   - 通过环境变化呼应心理转变
   - 确保转折有铺垫且符合人物心理逻辑

5. 环境渲染场景（占比10%）：
   - 环境描写需呼应角色情绪
   - 通过光线、气味、声音等元素创造氛围
   - 选择具有情感隐喻的场景元素
''';

  /// 获取女性向短篇小说的大纲生成提示词
  static String getShortNovelOutlinePrompt(String title, String genre, String theme, int wordCount) {
    return '''
请为这部名为《$title》的$genre短篇小说创作一个富有情感深度的大纲。

创作要求：
$theme
总字数控制在$wordCount字左右

大纲格式要求：
1. 整体架构（情感主题与关系核心）
   - 核心情感：主要情感基调和变化路径
   - 关系发展：人物关系的演变轨迹
   - 环境设定：情感发展的背景氛围
   
2. 五段式情感结构
   A. 情感基调与人物引入（总字数的15%）：
      - 主角的初始情感状态和心理需求
      - 人际关系的初始格局
      - 引发情感变化的关键触发事件
   
   B. 情感探索与试探（总字数的20%）：
      - 主要关系的初步互动和试探
      - 自我认知的初步挑战
      - 潜在情感障碍的显现
   
   C. 情感深化与冲突（总字数的30%）：
      - 关系的逐步深入和复杂化
      - 价值观和需求的碰撞
      - 内心矛盾与外部压力的交织
   
   D. 情感危机与抉择（总字数的20%）：
      - 核心关系的重大考验
      - 关键价值选择与自我认知
      - 情感突破或转变的契机
   
   E. 情感新生与余韵（总字数的15%）：
      - 关系的新平衡或重新定义
      - 情感认知的提升与成长
      - 余韵与未来的暗示

3. 情感场景设计
   - 2-3处情感转折的关键场景
   - 情感氛围的渐变设计
   - 象征性道具或场景的运用

4. 人物情感勾勒
   - A角色：情感特质和成长方向
   - B角色：关系互动方式和情感表达方式
   - 其他角色：与核心关系的互动和影响

请确保大纲具有情感的层次感和发展的自然流畅性，为后续创作留有深入探索的空间。
''';
  }

  /// 获取女性向短篇小说各部分生成提示词
  static String getShortNovelPartPrompt(
    String title,
    String genre,
    String partTitle,
    String partOutline,
    String previousContext,
    String nextContext,
    int targetWordCount,
    bool isFirstPart,
    bool isLastPart,
  ) {
    return '''
请根据以下大纲和要求，创作短篇小说《$title》的"$partTitle"部分：

当前部分大纲：
$partOutline

${previousContext.isNotEmpty ? '前文内容提示：\n$previousContext\n\n' : ''}
${nextContext.isNotEmpty ? '后续内容走向：\n$nextContext\n\n' : ''}

创作要求：
1. 本部分字数在$targetWordCount字左右
2. 严格按照大纲内容展开，确保情感发展的连贯性
3. 文风要符合$genre类型的情感特点
4. ${isFirstPart ? '开头要建立鲜明的情感基调和氛围，引入核心人物关系' : 
    isLastPart ? '结局要留有情感余韵，给读者思考和感受的空间' : 
    '保持情感层次的丰富性，推动关系自然发展'}
5. 通过细节描写展现人物的内心变化，保持7:3的心理描写与外部行动比例
6. 注重感官描写，尤其是触觉、嗅觉和听觉的细腻表达
7. 对话要自然且富有潜台词，传递情感的微妙变化
8. 环境描写要呼应人物情感状态，创造沉浸式氛围

请直接输出小说内容，不需要包含标题或部分编号：
''';
  }

  /// 获取女性向短篇小说的角色设计提示词
  static String getCharacterPrompt() {
    return '''
请为女性向短篇小说设计富有情感深度的角色。角色设计应考虑以下几个方面：

1. 情感画像：
   - 独特的情感表达方式
   - 情感伤痕和防御机制
   - 隐藏的情感需求和渴望
   - 情感触发点和反应模式

2. 外在与内在的反差：
   - 表面形象与内心世界的对比
   - 公众面具与私人状态的差异
   - 言行不一致中透露的真实情感
   - 防御机制背后的脆弱点

3. 成长与阻碍：
   - 情感成长的关键障碍
   - 需要突破的自我认知盲点
   - 最需要治愈的心理创伤
   - 潜在的成长方向和可能性

4. 关系模式：
   - 与重要他人的互动模式
   - 吸引和排斥的矛盾心理
   - 亲密关系中的恐惧和期待
   - 关系处理的习惯性策略

5. 细节特征：
   - 能体现性格的外表细节
   - 独特的肢体语言和习惯动作
   - 特有的语言习惯和表达方式
   - 反映内心的个人空间布置

每个角色都应具备足够的情感深度和复杂性，能引起读者的共鸣和思考，支撑故事的情感发展。
''';
  }

  /// 期待感构建提示词
  static String get expectationPrompt => '''
在女性向短篇小说的创作中，必须有意识地构建和维持读者的情感期待感。请在以下几个维度构建期待感：

1. 关系发展期待：
   - 在初遇时埋设关系潜质的暗示
   - 通过第三方角色的反应衬托关系可能性
   - 安排关键时刻的情感选择点
   - 保持关系状态的微妙不确定性

2. 心理突破期待：
   - 暗示角色内心的核心创伤或困境
   - 设置心理防线被突破的条件
   - 在关键场景中创造自我认知的机会
   - 预留情感宣泄或领悟的高潮时刻

3. 情感共鸣期待：
   - 在开篇建立读者与角色的情感连接
   - 设计能引起强烈共情的两难处境
   - 安排读者能投射自身体验的场景
   - 留下关于情感本质的思考空间

4. 和解与成长期待：
   - 明确角色需要面对的心理枷锁
   - 设置自我和解的必要条件
   - 暗示成长后的新平衡状态
   - 为关系的未来留下开放式可能

5. 情感细节期待：
   - 设置关键的象征性物品或场景
   - 创造期待已久的情感仪式感时刻
   - 预留重要对话或表白的时机
   - 安排情感线索的巧妙回收点

有效的情感期待感构建将使读者产生强烈的情感投入，增强故事的共鸣和感染力。
''';
} 