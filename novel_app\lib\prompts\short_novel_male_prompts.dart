class ShortNovelMalePrompts {
  /// 男性向短篇小说基本原则
  static String get basicPrinciples => '''
在创作男性向短篇小说时，请遵循以下核心原则：

1. 节奏控制：
   - 开篇2000字内必须触及核心冲突，采用"黄金三章定律"
   - 动作描写与智斗博弈保持3:7比例
   - 关键转折点设置在总字数的40%和70%处
   - 结局预留10-15%篇幅进行余韵处理

2. 人物塑造：
   - 主角必须有明确的标志性行为特征（如紧张时转动特定物品）
   - 添加"职业病"式细节，增强代入感
   - 配角应各有特色，避免"功能性人物"
   - 反派应在前三分之一篇幅提前露面或暗示存在

3. 世界观构建：
   - 保持设定的一致性，尤其是科技水平和社会规则
   - 合理设计势力架构，2-3个对立方相互制衡
   - 设置具有标志性的场景和环境元素
   - 核心矛盾要有足够的格局和冲突张力

4. 叙事技巧：
   - 埋设明显的悬念，但解决方式要出人意料
   - 设计1-2处"高能景点"，增强记忆点
   - 制造必要的交互困境和选择压力
   - 通过"感官组合拳"增强场景沉浸感
''';

  /// 男性向短篇小说的类型特点
  static const Map<String, String> genreCharacteristics = {
    '科幻': '构建严谨的科技体系，探索人与科技的关系，设置具有哲学深度的伦理困境',
    '玄幻': '创造独特的修炼体系，合理的世界规则，强调力量进阶与突破',
    '悬疑': '设置巧妙的案件/谜题，预留足够线索，局部反转层层递进',
    '都市': '社会阶层的真实冲突，职场和生活的双线发展，具有现实社会洞察力',
    '游戏': '明确的规则体系，技能与策略的运用，公平竞争中的智力博弈',
    '历史': '尊重基本史实，在历史夹缝中合理展开故事，展现特定时代特色',
    '军事': '注重军事细节的准确性，体现组织纪律与个人英雄主义的平衡',
    '竞技': '专业领域知识展示，挑战与超越极限的历程，团队协作与个人突破',
  };

  /// 男性向短篇小说的写作风格
  static const Map<String, String> writingStyles = {
    '硬核': '严谨的逻辑，专业的领域知识，强调思辨和技术性',
    '热血': '紧凑的节奏，激烈的冲突，直面挑战的勇气',
    '冷峻': '克制的情感表达，高效的行动描写，简洁有力的对话',
    '沉稳': '厚重的历史感，细腻的环境描写，深沉的人文关怀',
    '机智': '巧妙的反转，灵活的应对，智慧解决问题的过程',
    '黑暗': '残酷的现实，道德的灰色地带，生存与抉择的考验',
    '幽默': '巧妙的讽刺，轻松的对话，角色间的俏皮互动',
  };

  /// 男性向短篇小说的情节模式
  static const List<String> plotPatterns = [
    '挑战-失败-成长-再战-胜利',
    '平凡-异变-抗争-牺牲-超越',
    '危机-应对-转机-反击-新局',
    '谜题-探索-误判-反转-真相',
    '诱惑-陷落-觉醒-救赎-平衡',
    '相遇-冲突-合作-背叛-重建',
  ];

  /// 男性向短篇小说场景调控建议
  static String get sceneControlSuggestions => '''
场景布局建议：

1. 动态场景（占比30%）：
   - 设置视觉焦点和运动线条
   - 通过远近景切换增强空间感
   - 运用声音和光影烘托氛围

2. 交锋场景（占比25%）：
   - 明确的对抗目标和双方优劣势
   - 3-5轮的攻防交替，形成节奏
   - 出其不意的环境因素介入

3. 探索场景（占比20%）：
   - 设置信息点和发现的层次感
   - 通过细节透露更大的世界观
   - 制造适当的未知和危险感

4. 对话场景（占比15%）：
   - 控制每段对话在3-5轮内
   - 通过言行不一致制造角色层次
   - 在对话中穿插微小动作和表情

5. 内省场景（占比10%）：
   - 心理活动与外部环境结合
   - 控制篇幅，避免过度沉浸
   - 通过回忆闪回增强厚度
''';

  /// 获取男性向短篇小说的大纲生成提示词
  static String getShortNovelOutlinePrompt(String title, String genre, String theme, int wordCount) {
    return '''
请为这部名为《$title》的$genre短篇小说创作一个结构严谨、紧凑的大纲。

创作要求：
$theme
总字数控制在$wordCount字左右

大纲格式要求：
1. 整体架构（核心矛盾与主题）
   - 中心冲突：主要对抗关系和核心矛盾
   - 成长路径：主角需要克服的障碍和成长方向
   - 世界设定：必要的背景和规则设定
   
2. 五段式故事结构
   A. 开篇与背景铺垫（总字数的15%）：
      - 人物初始状态和能力基准
      - 世界规则和背景介绍
      - 引发故事的关键事件
   
   B. 冲突展开（总字数的20%）：
      - 主角面临的挑战和考验
      - 初步尝试及其结果
      - 对立面的正式登场
   
   C. 情节发展与转折（总字数的30%）：
      - 多方势力的交织与博弈
      - 关键信息的揭示或获取
      - 计划的执行与阻碍
   
   D. 高潮与危机（总字数的20%）：
      - 最强对手或最大挑战
      - 关键抉择与代价
      - 形势逆转的契机
   
   E. 结局与收尾（总字数的15%）：
      - 最终对决或问题解决
      - 伏笔回收与真相揭示
      - 角色成长与世界变化

3. 场景设计要点
   - 2-3处标志性场景详细描绘
   - 动静交替的节奏安排
   - 适当设置反转和悬念

4. 人物勾勒
   - 主角：明确的能力特点和成长需求
   - 对手：有力的威胁和明确的动机
   - 关键配角：独特的功能和特点

请确保大纲具有足够的张力和节奏感，同时为后续创作留有发挥空间。
''';
  }

  /// 获取男性向短篇小说各部分生成提示词
  static String getShortNovelPartPrompt(
    String title,
    String genre,
    String partTitle,
    String partOutline,
    String previousContext,
    String nextContext,
    int targetWordCount,
    bool isFirstPart,
    bool isLastPart,
  ) {
    return '''
请根据以下大纲和要求，创作短篇小说《$title》的"$partTitle"部分：

当前部分大纲：
$partOutline

${previousContext.isNotEmpty ? '前文内容提示：\n$previousContext\n\n' : ''}
${nextContext.isNotEmpty ? '后续内容走向：\n$nextContext\n\n' : ''}

创作要求：
1. 本部分字数在$targetWordCount字左右
2. 严格按照大纲内容展开，确保情节连贯
3. 文风要符合$genre类型特点
4. ${isFirstPart ? '开头要迅速引入核心冲突，设立明确的悬念钩子' : 
    isLastPart ? '结局要有力完整，所有关键情节得到合理收束' : 
    '维持情节张力和节奏感，推动故事自然发展'}
5. 增强场景的感官体验，至少调动3种感官描写
6. 通过细节和独特习惯展现人物个性
7. 保持3:7的动作描写与智斗博弈比例
8. 适当设置微型悬念和转折

请直接输出小说内容，不需要包含标题或部分编号：
''';
  }

  /// 获取男性向短篇小说的角色设计提示词
  static String getCharacterPrompt() {
    return '''
请为男性向短篇小说设计立体鲜明的角色。角色设计应考虑以下几个方面：

1. 外在特征与行为标志：
   - 独特且能反映性格的外表特征
   - 标志性的习惯动作或行为模式
   - 特有的说话方式和表达习惯
   - 专业领域的"职业病"表现

2. 能力与限制：
   - 明确的核心能力和专长
   - 能力使用的规则和代价
   - 关键弱点和致命缺陷
   - 能力进阶的潜在方向

3. 内在动机与冲突：
   - 驱动角色行动的核心动机
   - 个人价值观与外部世界的冲突
   - 理想与现实之间的张力
   - 内心深处的恐惧和渴望

4. 关系网络：
   - 与其他角色的关键联系
   - 盟友和对手的动态关系
   - 信任与背叛的可能性
   - 团队中的定位和作用

5. 成长轨迹：
   - 角色起点状态的明确描述
   - 需要克服的核心困境
   - 关键成长点的设计
   - 蜕变后与初始状态的对比

每个角色都应当有足够的深度和魅力，能够支撑故事发展并给读者留下深刻印象。
''';
  }

  /// 期待感构建提示词
  static String get expectationPrompt => '''
在男性向短篇小说的创作中，必须有意识地构建和维持读者的期待感。请在以下几个维度构建期待感：

1. 能力期待：
   - 在开篇暗示主角的潜力上限
   - 设置明确的能力成长节点
   - 预留"必杀技"或关键能力的释放时机
   - 通过对手实力展示来衬托成长空间

2. 对抗期待：
   - 早期引入终极对手的暗示
   - 设置多层次的敌人阶梯
   - 预设关键对决的场景和条件
   - 埋设双方的特殊联系或宿命关系

3. 谜题期待：
   - 提出核心谜题并给出部分线索
   - 安排线索逐步揭示的节奏
   - 设置1-2个有力的信息反转点
   - 预留最终真相的部分模糊空间

4. 结局期待：
   - 明确故事的目标和可能结局
   - 设置达成目标的障碍和代价
   - 保持结局形式的不确定性
   - 为可能的后续发展留下余地

5. 转折期待：
   - 在平稳发展中埋设危机伏笔
   - 创造形势逆转的关键点
   - 设计出人意料但合理的转机
   - 保持命运与个人选择的张力

有效的期待感构建将使读者产生强烈的阅读推动力，增强故事的沉浸感和吸引力。
''';
} 