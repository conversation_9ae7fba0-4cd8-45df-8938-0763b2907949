import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/novel_controller.dart';

class GeneratorScreen extends StatelessWidget {
  final novelController = Get.find<NovelController>();
  final _titleController = TextEditingController();
  final _promptController = TextEditingController();
  final _selectedLength = '中篇'.obs;
  final _selectedStyle = '硬科幻'.obs;

  GeneratorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('创作新作品'),
      ),
      body: Obx(() {
        if (novelController.isGenerating.value) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  value: novelController.generationProgress.value,
                ),
                const SizedBox(height: 16),
                Text(
                  novelController.generationStatus.value,
                  style: const TextStyle(fontSize: 16),
                ),
                Text(
                  '${(novelController.generationProgress.value * 100).toInt()}%',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView(
          padding: const EdgeInsets.all(16),
          children: [
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: '作品标题',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _promptController,
              decoration: const InputDecoration(
                labelText: '创作提示',
                border: OutlineInputBorder(),
                helperText: '描述你想要创作的故事情节、背景、人物等',
              ),
              maxLines: 5,
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '作品设置',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text('作品长度'),
                    Wrap(
                      spacing: 8,
                      children: ['短篇', '中篇', '长篇'].map((length) {
                        return ChoiceChip(
                          label: Text(length),
                          selected: _selectedLength.value == length,
                          onSelected: (selected) {
                            if (selected) {
                              _selectedLength.value = length;
                            }
                          },
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 16),
                    const Text('作品风格'),
                    Wrap(
                      spacing: 8,
                      children: ['硬科幻', '软科幻', '赛博朋克', '太空歌剧'].map((style) {
                        return ChoiceChip(
                          label: Text(style),
                          selected: _selectedStyle.value == style,
                          onSelected: (selected) {
                            if (selected) {
                              _selectedStyle.value = style;
                            }
                          },
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            FilledButton(
              onPressed: () {
                // 获取输入
                final title = _titleController.text.trim();
                final prompt = _promptController.text.trim();
                final length = _selectedLength.value;
                final style = _selectedStyle.value;

                if (title.isEmpty) {
                  Get.snackbar('错误', '请输入作品标题');
                  return;
                }
                if (prompt.isEmpty) {
                  Get.snackbar('错误', '请输入创作提示');
                  return;
                }

                // 更新 Controller 状态
                novelController.updateTitle(title);
                // 使用 prompt 作为背景或主题
                novelController.updateBackground(prompt);
                novelController.updateStyle(style);
                // 假设风格可以作为类型处理，或者需要单独设置
                novelController.setSelectedGenres([style]);

                // 判断是否为短篇小说
                final bool isShort = (length == '短篇');
                // 对于中篇/长篇，我们假设需要先生成大纲，或者直接调用 startGeneration (如果已有大纲)
                // 为了简化，这里统一调用 generateNovel，让它内部处理

                print('开始生成: title=$title, isShort=$isShort');

                // 设置控制器的短篇小说状态
                novelController.toggleShortNovel(isShort);
                if (isShort) {
                  // 设置短篇小说字数
                  novelController.updateShortNovelWordCount(15000); // 默认1.5万字
                }

                // 调用恢复后的生成方法
                novelController.generateNovel(isShortNovel: isShort);
              },
              child: const Padding(
                padding: EdgeInsets.all(16),
                child: Text(
                  '开始创作',
                  style: TextStyle(fontSize: 18),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }
}
